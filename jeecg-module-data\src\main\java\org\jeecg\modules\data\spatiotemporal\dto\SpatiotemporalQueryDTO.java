package org.jeecg.modules.data.spatiotemporal.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 时空搜图查询参数DTO
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
@Schema(description = "时空搜图查询参数")
public class SpatiotemporalQueryDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 经度
     */
    @Schema(description = "经度", example = "102.878487")
    private Double longitude;
    
    /**
     * 纬度
     */
    @Schema(description = "纬度", example = "25.054841")
    private Double latitude;
    
    /**
     * 搜索半径(米)
     */
    @Min(value = 0, message = "搜索半径不能小于0")
    @Max(value = 1000000, message = "搜索半径不能大于1000000米")
    @Schema(description = "搜索半径(米)", example = "200")
    private Integer radius = 200;
    
    /**
     * 时间范围
     * current_month - 本月
     * two_months - 近两月
     * three_months - 近三月
     * half_year - 半年
     * 或者直接传入日期范围，格式：yyyy-MM-dd,yyyy-MM-dd
     */
    @Pattern(regexp = "^(current_month|two_months|three_months|half_year|(\\d{4}-\\d{2}-\\d{2})(,\\d{4}-\\d{2}-\\d{2})?)$", 
            message = "时间范围参数无效，应为预定义值或日期范围格式")
    @Schema(description = "时间范围", example = "current_month", 
           allowableValues = {"current_month", "two_months", "three_months", "half_year", "yyyy-MM-dd,yyyy-MM-dd"})
    private String timeRange = "current_month";
    
    /**
     * 开始时间，直接指定时间范围
     */
    @Pattern(regexp = "^(\\d{4}-\\d{2}-\\d{2})?$", message = "开始时间格式错误，应为yyyy-MM-dd")
    @Schema(description = "开始时间，格式：yyyy-MM-dd", example = "2023-01-01")
    private String startDate;
    
    /**
     * 结束时间，直接指定时间范围
     */
    @Pattern(regexp = "^(\\d{4}-\\d{2}-\\d{2})?$", message = "结束时间格式错误，应为yyyy-MM-dd")
    @Schema(description = "结束时间，格式：yyyy-MM-dd", example = "2023-06-10")
    private String endDate;
    
    /**
     * 地图引擎类型
     */
    @Pattern(regexp = "^(amap|mars3d)$", message = "地图引擎类型参数无效")
    @Schema(description = "地图引擎类型", example = "amap", allowableValues = {"amap", "mars3d"})
    private String engineType = "amap";
    
    /**
     * 当前页
     */
    @Min(value = 1, message = "当前页不能小于1")
    @Schema(description = "当前页", example = "1")
    private Integer current = 1;
    
    /**
     * 每页记录数
     */
    @Min(value = 1, message = "每页记录数不能小于1")
    @Max(value = 100, message = "每页记录数不能大于100")
    @Schema(description = "每页记录数", example = "10")
    private Integer pageSize = 10;
    
    /**
     * 校验日期是否合法
     * @return 是否有效
     */
    public boolean isDateRangeValid() {
        // 如果都未设置，使用timeRange计算，默认有效
        if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
            // 检查timeRange是否为日期范围格式
            if (StringUtils.isNotBlank(timeRange) && timeRange.contains(",")) {
                String[] dates = timeRange.split(",");
                if (dates.length == 2) {
                    return dates[0].matches("\\d{4}-\\d{2}-\\d{2}") 
                        && dates[1].matches("\\d{4}-\\d{2}-\\d{2}");
                } else if (dates.length == 1) {
                    // 处理只有一个日期的情况
                    return dates[0].matches("\\d{4}-\\d{2}-\\d{2}");
                }
                return false;
            }
            return true;
        }
        
        // 如果设置了startDate和endDate，则两者都必须设置且格式正确
        return StringUtils.isNotBlank(startDate) 
            && StringUtils.isNotBlank(endDate)
            && startDate.matches("\\d{4}-\\d{2}-\\d{2}")
            && endDate.matches("\\d{4}-\\d{2}-\\d{2}");
    }
} 