package org.jeecg.modules.data.spatiotemporal.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.io.IOUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.data.spatiotemporal.dto.SpatiotemporalQueryDTO;
import org.jeecg.modules.data.spatiotemporal.entity.FlightInspectionImage;
import org.jeecg.modules.data.spatiotemporal.service.IMinioSyncService;
import org.jeecg.modules.data.spatiotemporal.service.ISpatiotemporalService;
import org.jeecg.modules.data.spatiotemporal.vo.HeatmapResponseVO;
import org.jeecg.modules.flight.service.IFlightInspectionImagesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.jeecg.common.system.util.JwtUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 时空搜图控制器
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/data/inspection")
@Tag(name = "时空搜图", description = "航拍图片时空搜索API")
public class SpatiotemporalController {

    private final ISpatiotemporalService spatiotemporalService;
    private final IMinioSyncService minioSyncService;

    @Autowired
    private IFlightInspectionImagesService flightInspectionImagesService;


    /**
     * 获取热力图数据
     */
    @RequestMapping(value = "/getHeatmapData", method = RequestMethod.POST)
    @Operation(summary = "获取热力图数据", description = "根据时间范围获取热力图数据")
    public Result<HeatmapResponseVO> getHeatmapData(@Valid @RequestBody SpatiotemporalQueryDTO queryDTO) {
        log.info("获取热力图数据，查询参数: [timeRange={}, startDate={}, endDate={}]",
                queryDTO.getTimeRange(), queryDTO.getStartDate(), queryDTO.getEndDate());
        try {
            // 检查日期范围是否有效（已包含对timeRange日期格式的检查）
            if (!queryDTO.isDateRangeValid()) {
                return Result.error("日期格式错误或日期范围不完整");
            }

            // 处理timeRange包含日期范围的情况
            processTimeRangeIfDateFormat(queryDTO);

            // 检查经纬度参数
            if (queryDTO.getLongitude() == null || queryDTO.getLatitude() == null) {
                log.info("热力图请求中经纬度参数为null，将使用默认中心点");
            }

            HeatmapResponseVO heatmapData = spatiotemporalService.getHeatmapData(queryDTO);

            // 记录返回数据情况
            int dataCount = heatmapData != null && heatmapData.getRecords() != null ?
                    heatmapData.getRecords().size() : 0;
            log.info("热力图数据查询完成，返回 {} 条记录", dataCount);

            // 确保返回成功的响应
            return Result.OK(heatmapData);
        } catch (Exception e) {
            log.error("获取热力图数据异常", e);
            return Result.error("获取热力图数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取热力图颜色配置
     */
    @RequestMapping(value = "/getHeatmapColorConfig", method = RequestMethod.POST)
    @Operation(summary = "获取热力图颜色配置", description = "获取热力图颜色配置信息")
    public Result<Map<String, String>> getHeatmapColorConfig() {
        try {
            Map<String, String> colorConfig = spatiotemporalService.getHeatmapColorConfig();
            return Result.OK(colorConfig);
        } catch (Exception e) {
            log.error("获取热力图颜色配置异常", e);
            return Result.error("获取热力图颜色配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取初始中心点
     */
    @RequestMapping(value = "/getInitialCenter", method = RequestMethod.GET)
    @Operation(summary = "获取初始中心点", description = "获取地图默认中心点")
    public Result<Map<String, Object>> getInitialCenter() {
        try {
            Map<String, Object> center = spatiotemporalService.getInitialCenter();
            return Result.OK(center);
        } catch (Exception e) {
            log.error("获取初始中心点异常", e);
            return Result.error("获取初始中心点失败: " + e.getMessage());
        }
    }

    /**
     * 同步MinIO图片到数据库
     */
    @PostMapping("/syncMinioImages")
    //@RequiresPermissions("data:data_inspection_list:sync")
    @Operation(summary = "同步MinIO图片", description = "将MinIO桶中的特定任务ID下的图片同步到数据库")
    public Result<Map<String, Object>> syncMinioImages(
            @RequestParam(value = "bucketName", required = false) String bucketName,
            @RequestParam(value = "taskId", required = true) String taskId,
            @RequestParam(value = "defaultLocation", required = false) Map<String, Double> defaultLocation,
            @RequestParam(value = "creator", required = false) String creator,
            @RequestParam(value = "updater", required = false) String updater,
            @RequestParam(value = "orgCode", required = false) String orgCode,
            @RequestParam(value = "preferBucketInfo", required = false, defaultValue = "true") Boolean preferBucketInfo,
            @RequestParam(value = "enableNewPathFormat", required = false, defaultValue = "false") Boolean enableNewPathFormat,
            @RequestParam(value = "newFormatPrefixes", required = false) List<String> newFormatPrefixes,
            @RequestParam(value = "force", required = false, defaultValue = "false") Boolean force) {

        // 强制校验taskId参数
        if (StringUtils.isBlank(taskId)) {
            return Result.error("任务ID不能为空，必须提供有效的任务ID");
        }

        // 验证taskId格式
        if (!taskId.trim().matches("^\\d+$")) {
            return Result.error("任务ID格式无效，必须是纯数字格式，例如：1920681769136152577");
        }

        // 获取当前用户信息
        String username = "unknown";
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String token = request.getHeader("X-Access-Token");
            if (StringUtils.isNotBlank(token)) {
                username = JwtUtil.getUsername(token);
            }
        } catch (Exception e) {
            // 忽略异常
        }

        // 打印明确的同步开始边框和提示
        log.info("======================================================");
        log.info("                MinIO同步任务开始                     ");
        log.info("======================================================");
        log.info("请求用户: {}", username);
        log.info("请求时间: {}", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        log.info("同步参数: 桶={}, 任务ID={}, 默认位置={}, 创建人={}",
                StringUtils.isBlank(bucketName) ? "默认桶" : bucketName,
                taskId,
                defaultLocation,
                StringUtils.isBlank(creator) ? "admin" : creator);
        log.info("优先使用桶信息: {}", preferBucketInfo);
        log.info("启用新路径格式支持: {}", enableNewPathFormat);
        if (newFormatPrefixes != null && !newFormatPrefixes.isEmpty()) {
            log.info("新格式路径前缀: {}", newFormatPrefixes);
        }

        // 记录任务ID同步特别提示
        log.info("**********************************");
        log.info("将同步任务ID {} 相关的图片", taskId);
        log.info("支持传统路径和djicloudapi/wayline路径格式");
        log.info("**********************************");

        log.info("======================================================");

        log.info("请求同步MinIO图片到数据库，桶: {}, 任务ID: {}, 默认位置: {}, 创建人: {}, 优先使用桶信息: {}, 强制同步: {}",
                bucketName, taskId, defaultLocation, creator, preferBucketInfo, force);
        try {
            // 设置默认值
            if (defaultLocation == null) {
                defaultLocation = new HashMap<>();
                defaultLocation.put("latitude", 25.059399);
                defaultLocation.put("longitude", 102.878423);
            }

            // 检查并处理任务ID
            String formattedTaskId = null;
            if (StringUtils.isNotBlank(taskId)) {
                formattedTaskId = taskId.trim();

                // 验证任务ID是否为有效的格式（通常是数字）
                boolean isValidNumericFormat = formattedTaskId.matches("^\\d+$");
                log.info("任务ID格式验证: [{}] 是否为有效数字格式: {}", formattedTaskId, isValidNumericFormat ? "是" : "否");

                // 检查任务ID是否含有无效字符
                if (formattedTaskId.contains(" ") || formattedTaskId.contains("\t") || formattedTaskId.contains("\n")) {
                    log.warn("任务ID [{}] 包含空白字符，已自动清除", taskId);
                    formattedTaskId = formattedTaskId.replaceAll("\\s+", "");
                }

                log.info("将严格按照任务ID {} 过滤图片数据，只处理该任务文件夹下的图片", formattedTaskId);
            }

            String defaultCreator = creator != null ? creator : "admin";
            String defaultUpdater = updater != null ? updater : "admin";
            String defaultOrgCode = orgCode != null ? orgCode : "A01";

            // 传递默认配置到服务层
            Map<String, Object> defaultConfig = new HashMap<>();
            defaultConfig.put("defaultLocation", defaultLocation);
            defaultConfig.put("creator", defaultCreator);
            defaultConfig.put("updater", defaultUpdater);
            defaultConfig.put("orgCode", defaultOrgCode);
            defaultConfig.put("preferBucketInfo", preferBucketInfo);

            // 添加新格式支持配置
            defaultConfig.put("enableNewPathFormat", enableNewPathFormat);
            if (newFormatPrefixes != null && !newFormatPrefixes.isEmpty()) {
                defaultConfig.put("newFormatPrefixes", newFormatPrefixes);
            } else {
                // 设置默认的新格式前缀
                defaultConfig.put("newFormatPrefixes", Arrays.asList("djicloudapi/wayline"));
            }

            // 获取同步前数据库中的图片数量
            int beforeCount = spatiotemporalService.getTotalImagesCount();

            // 调用MinioSyncService执行同步，传入默认配置
            Map<String, Integer> result = minioSyncService.syncImagesFromMinio(bucketName, formattedTaskId, defaultConfig);

            // 获取同步后数据库中的图片数量
            int afterCount = spatiotemporalService.getTotalImagesCount();

            // 计算实际增加的图片数量
            int actualIncrease = afterCount - beforeCount;

            // 打印详细的同步统计信息
            log.info("======================================================");
            log.info("            MinIO图片同步完成统计信息                  ");
            log.info("======================================================");
            log.info("同步前数据库图片总数: {}", beforeCount);
            log.info("同步后数据库图片总数: {}", afterCount);
            log.info("实际新增图片数量: {}", actualIncrease);
            log.info("处理明细:");
            log.info("  - 新增图片: {}", result.getOrDefault("inserted", 0));
            log.info("  - 更新图片: {}", result.getOrDefault("updated", 0));
            log.info("  - 跳过图片: {}", result.getOrDefault("skipped", 0));
            log.info("  - 错误数量: {}", result.getOrDefault("errors", 0));
            log.info("  - 总处理量: {}", result.values().stream().mapToInt(Integer::intValue).sum());

            // 添加耗时信息
            int elapsedSeconds = result.getOrDefault("elapsedSeconds", 0);
            int totalObjects = result.getOrDefault("objectCount", 0);
            int totalProcessed = result.getOrDefault("total", 0);

            log.info("性能统计:");
            log.info("  - 总处理对象: {}", totalObjects);
            log.info("  - 总处理图片: {}", totalProcessed);
            log.info("  - 总耗时: {}秒", elapsedSeconds);
            if (elapsedSeconds > 0) {
                double speed = (double) totalProcessed / elapsedSeconds;
                log.info("  - 平均速度: {}张/秒", String.format("%.2f", speed));
            }
            log.info("  - 完成时间: {}", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            log.info("======================================================");

            return Result.OK(new HashMap<>(result));
        } catch (Exception e) {
            log.error("同步MinIO图片失败", e);
            if (force) {
                // 强制模式下返回部分结果
                Map<String, Object> partialResult = new HashMap<>();
                partialResult.put("errors", 1);
                partialResult.put("errorMessage", e.getMessage());
                return Result.OK(partialResult); // 强制模式下即使有错误也返回成功
            } else {
                return Result.error("同步失败: " + e.getMessage()); // 普通模式返回错误
            }
        }
    }

    /**
     * 根据位置查询图片
     */
    @PostMapping("/queryImages")
    @Operation(summary = "根据位置查询图片", description = "根据经纬度和半径查询附近的图片，支持分页和排序参数")
    public Result<Page<FlightInspectionImage>> queryImages(
            @Valid @RequestBody SpatiotemporalQueryDTO queryDTO,
            @RequestParam(value = "needPaging", defaultValue = "true") Boolean needPaging,
            @RequestParam(value = "sortByCaptureTimeAsc", defaultValue = "true") Boolean sortByCaptureTimeAsc) {
        log.info("根据位置查询图片，参数: [lng={}, lat={}, radius={}, timeRange={}, needPaging={}, sortByCaptureTimeAsc={}]",
                queryDTO.getLongitude(), queryDTO.getLatitude(), queryDTO.getRadius(),
                queryDTO.getTimeRange(), needPaging, sortByCaptureTimeAsc);
        try {
            // 检查日期范围是否有效（已包含对timeRange日期格式的检查）
            if (!queryDTO.isDateRangeValid()) {
                return Result.error("日期格式错误或日期范围不完整");
            }

            // 处理timeRange包含日期范围的情况
            processTimeRangeIfDateFormat(queryDTO);

            // 时空搜图功能必须有经纬度
            if (queryDTO.getLongitude() == null || queryDTO.getLatitude() == null) {
                log.warn("时空搜图请求中经纬度参数为null，无法执行空间查询");
                return Result.error("进行时空搜图时经纬度参数不能为空");
            }

            // 确保半径参数有效，默认为200米
            if (queryDTO.getRadius() == null || queryDTO.getRadius() <= 0) {
                log.info("搜索半径未指定或无效，设置为默认值200米");
                queryDTO.setRadius(200);
            }

            // 调用统一的查询方法
            Page<FlightInspectionImage> page = spatiotemporalService.queryImages(
                    queryDTO, needPaging, sortByCaptureTimeAsc);

            // 为每个图片生成完整的URL
            if (page != null && page.getRecords() != null) {
                for (FlightInspectionImage image : page.getRecords()) {
                    if (StringUtils.isNotBlank(image.getStoragePath())) {
                        // 确保路径总是带有前导斜杠
                        String storagePath = image.getStoragePath();
                        if (!storagePath.startsWith("/")) {
                            storagePath = "/" + storagePath;
                            // 更新数据库中的记录，确保路径格式一致
                            image.setStoragePath(storagePath);
                        }

                        // 清理存储路径，移除开头的斜杠（MinIO访问不需要前导斜杠）
                        String cleanPath = storagePath.replaceAll("^/+", "");

                        // 生成完整的MinIO URL
                        String imageUrl = String.format("%s/%s/%s",
                                "http://**************:9000",  // MinIO服务器地址
                                "djicloudapi",                 // 存储桶名称
                                cleanPath);
                        image.setImageUrl(imageUrl);  // 设置完整的图片URL
                    }
                }
            }

            return Result.OK(page);
        } catch (Exception e) {
            log.error("根据位置查询图片异常", e);
            return Result.error("查询图片失败: " + e.getMessage());
        }
    }



    /**
     * 批量删除图片
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除图片", description = "根据ID批量删除图片")
    public Result<Void> batchDelete(@NotBlank(message = "ID列表不能为空")
                                    @Pattern(regexp = "^\\d+(,\\d+)*$", message = "ID格式错误，应为以逗号分隔的数字列表")
                                    @RequestParam("ids") String ids) {
        log.info("批量删除图片，请求删除 {} 项", ids.split(",").length);
        try {
            if (StringUtils.isBlank(ids)) {
                return Result.error("ID列表不能为空");
            }

            boolean success = spatiotemporalService.batchDeleteImages(ids);
            if (success) {
                return Result.OK("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除图片异常", e);
            return Result.error("删除图片失败: " + e.getMessage());
        }
    }



    /**
     * 获取MinIO配置信息
     */
    @PostMapping("/config/minio")
    @Operation(summary = "获取MinIO配置", description = "获取MinIO服务器配置信息")
    public Result<Map<String, String>> getMinioConfig() {
        log.info("获取MinIO配置信息");
        try {
            Map<String, String> config = new HashMap<>();
            config.put("minioUrl", "http://**************:9000");
            config.put("minioBucket", "djicloudapi");

            return Result.OK(config);
        } catch (Exception e) {
            log.error("获取MinIO配置异常", e);
            return Result.error("获取MinIO配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取MinIO图片
     */
    @PostMapping("/config/minio/getImage")
    @Operation(summary = "获取MinIO图片", description = "根据图片ID获取MinIO中的图片")
    public void getMinioImage(@RequestParam("imageId") String imageId, HttpServletResponse response) {
        log.info("获取MinIO图片，图片ID: {}", imageId);
        try {
            // 从数据库获取图片信息
            FlightInspectionImage image = spatiotemporalService.getImageById(Long.parseLong(imageId));
            if (image == null || StringUtils.isBlank(image.getStoragePath())) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "图片不存在");
                return;
            }

            // 从MinIO获取图片
            try (InputStream inputStream = minioSyncService.getImageStream(image.getStoragePath())) {
                if (inputStream == null) {
                    response.sendError(HttpServletResponse.SC_NOT_FOUND, "图片不存在");
                    return;
                }

                // 设置响应头
                response.setContentType("image/jpeg");
                response.setHeader("Cache-Control", "max-age=31536000");

                // 将图片流写入响应
                IOUtils.copy(inputStream, response.getOutputStream());
            }
        } catch (Exception e) {
            log.error("获取MinIO图片异常", e);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "获取图片失败: " + e.getMessage());
            } catch (IOException ex) {
                log.error("发送错误响应失败", ex);
            }
        }
    }

    /**
     * 获取图片URL
     */
    @PostMapping("/getImageUrl")
    @Operation(summary = "获取图片URL", description = "根据图片ID获取完整的图片访问URL")
    public Result<String> getImageUrl(@RequestParam("imageId") Long imageId) {
        log.info("获取图片URL，图片ID: {}", imageId);
        try {
            // 从数据库获取图片信息
            FlightInspectionImage image = spatiotemporalService.getImageById(imageId);
            if (image == null || StringUtils.isBlank(image.getStoragePath())) {
                return Result.error("图片不存在或存储路径为空");
            }

            // 获取MinIO配置
            Map<String, String> minioConfig = new HashMap<>();
            minioConfig.put("minioUrl", "http://**************:9000");
            minioConfig.put("minioBucket", "djicloudapi");

            // 确保路径总是带有前导斜杠
            String storagePath = image.getStoragePath();
            if (!storagePath.startsWith("/")) {
                storagePath = "/" + storagePath;
            }

            // 清理存储路径，移除开头的斜杠（MinIO访问不需要前导斜杠）
            String cleanPath = storagePath.replaceAll("^/+", "");

            // 构建完整URL
            String imageUrl = String.format("%s/%s/%s",
                    minioConfig.get("minioUrl"),
                    minioConfig.get("minioBucket"),
                    cleanPath);

            return Result.OK(imageUrl);
        } catch (Exception e) {
            log.error("获取图片URL异常", e);
            return Result.error("获取图片URL失败: " + e.getMessage());
        }
    }

    /**
     * 获取MinIO桶中的图片数量
     */
    @GetMapping("/getMinioImageCount")
    @Operation(summary = "获取MinIO图片数量", description = "获取MinIO桶中的图片总数")
    public Result<Integer> getMinioImageCount(
            @RequestParam(value = "bucketName", required = false) String bucketName,
            @RequestParam(value = "prefix", required = false) String prefix) {
        log.info("获取MinIO图片数量，桶: {}, 前缀: {}", bucketName, prefix);
        try {
            int count = minioSyncService.getImageCountFromMinio(bucketName, prefix);
            return Result.OK(count);
        } catch (Exception e) {
            log.error("获取MinIO图片数量异常", e);
            return Result.error("获取图片数量失败: " + e.getMessage());
        }
    }

    /**
     * 处理timeRange参数包含日期格式的情况
     * 如果timeRange为日期范围格式（yyyy-MM-dd,yyyy-MM-dd），则提取并设置为startDate和endDate
     *
     * @param queryDTO 查询参数
     */
    private void processTimeRangeIfDateFormat(SpatiotemporalQueryDTO queryDTO) {
        String timeRange = queryDTO.getTimeRange();
        if (StringUtils.isNotBlank(timeRange) && timeRange.contains(",")
                && timeRange.matches("^\\d{4}-\\d{2}-\\d{2}(,\\d{4}-\\d{2}-\\d{2})?$")) {
            String[] dates = timeRange.split(",");
            if (dates.length >= 1) {
                // 如果startDate未设置，则使用timeRange中的第一个日期
                if (StringUtils.isBlank(queryDTO.getStartDate())) {
                    queryDTO.setStartDate(dates[0]);
                }

                // 如果endDate未设置且存在第二个日期，则使用timeRange中的第二个日期
                if (StringUtils.isBlank(queryDTO.getEndDate()) && dates.length >= 2) {
                    queryDTO.setEndDate(dates[1]);
                } else if (StringUtils.isBlank(queryDTO.getEndDate())) {
                    // 如果只有一个日期，则起止日期相同
                    queryDTO.setEndDate(dates[0]);
                }

                // 设置为预定义值，避免后续处理中再次解析timeRange
                queryDTO.setTimeRange("custom_date_range");
            }
        }
    }
}
