package org.jeecg.modules.data.spatiotemporal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.data.spatiotemporal.entity.FlightInspectionImage;
import org.jeecg.modules.data.spatiotemporal.vo.HeatPointVO;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;
import java.util.Map;

/**
 * 航拍图片数据访问层
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Mapper
@Qualifier("flightInspectionImageMapperPrimary")
public interface FlightInspectionImageMapper extends BaseMapper<FlightInspectionImage> {

    /**
     * 根据时间范围和区域获取热力图数据
     * 注意：当lng和lat为null时，将忽略空间距离计算，返回时间范围内的所有数据
     *
     * @param timeRangeStart 时间范围开始
     * @param timeRangeEnd   时间范围结束
     * @param lng            中心点经度 (可为null，此时忽略空间筛选)
     * @param lat            中心点纬度 (可为null，此时忽略空间筛选)
     * @param radius         半径（米）(当为0或null时，忽略空间筛选)
     * @return 热力图数据列表
     */
    List<HeatPointVO> getHeatmapData(@Param("timeRangeStart") String timeRangeStart,
                                     @Param("timeRangeEnd") String timeRangeEnd,
                                     @Param("lng") Double lng,
                                     @Param("lat") Double lat,
                                     @Param("radius") Integer radius);

    /**
     * 获取系统初始中心点
     *
     * @return 中心点坐标，键值为'key'
     */
    @MapKey("key")
    Map<String, Map<String, Object>> getInitialCenter();

    /**
     * 图片查询方法
     * @param page     分页参数
     * @param lng      经度
     * @param lat      纬度
     * @param radius   半径（米）
     * @param timeRangeStart 开始时间
     * @param timeRangeEnd   结束时间
     * @param needPaging 是否需要分页
     * @param sortByCaptureTimeAsc 是否按拍摄时间升序排列
     * @return 查询结果
     */
    Page<FlightInspectionImage> queryImages(Page<FlightInspectionImage> page,
                                            @Param("lng") Double lng,
                                            @Param("lat") Double lat,
                                            @Param("radius") Integer radius,
                                            @Param("timeRangeStart") String timeRangeStart,
                                            @Param("timeRangeEnd") String timeRangeEnd,
                                            @Param("needPaging") Boolean needPaging,
                                            @Param("sortByCaptureTimeAsc") Boolean sortByCaptureTimeAsc);

    /**
     * 批量删除图片
     *
     * @param idList 图片ID列表
     * @return 删除的记录数
     */
    int batchDeleteByIds(@Param("idList") List<Long> idList);

    /**
     * 更新图片标记状态
     *
     * @param id           图片ID
     * @param isAnnotated  标记状态（0未标记，1已标记）
     * @param updateBy     更新人
     * @return 更新的记录数
     */
    int updateImageMarkStatus(@Param("id") Long id,
                              @Param("isAnnotated") Integer isAnnotated,
                              @Param("updateBy") String updateBy);
}