package org.jeecg.modules.data.spatiotemporal.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.data.spatiotemporal.service.IMinioSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * MinIO图片同步定时任务
 * 定期从MinIO中同步图片数据到数据库
 */
@Slf4j
@Component
public class MinioSyncScheduler {

    @Autowired
    private IMinioSyncService minioSyncService;

    /**
     * 每天14:50执行自动同步
     * 使用智能批量同步模式，自动识别MinIO中所有任务的图片并分别导入
     * 同步失败的任务会被跳过，不影响其他任务的同步
     */
    @Scheduled(cron = "0 50 14 * * ?")
    public void scheduledMinioSync() {
        long startTime = System.currentTimeMillis();
        log.info("=== 开始执行定时MinIO自动同步任务 ===");
        log.info("同步模式: 精准同步（仅同步能识别taskId的文件，跳过无法识别的文件）");

        try {
            // 设置默认配置
            Map<String, Object> defaultConfig = createDefaultSyncConfig();

            // 执行智能批量同步（taskId=null表示同步所有图片）
            Map<String, Integer> resultInt = minioSyncService.syncImagesFromMinio(null, null, defaultConfig);
            Map<String, Object> result = new HashMap<>();
            resultInt.forEach((key, value) -> result.put(key, value));

            // 计算耗时
            long elapsedTime = System.currentTimeMillis() - startTime;
            long elapsedSeconds = elapsedTime / 1000;

            // 记录详细结果
            log.info("=== 定时自动同步完成 ===");
            log.info("同步结果统计:");
            log.info("  - 新增图片: {} 张", result.get("inserted"));
            log.info("  - 更新图片: {} 张", result.get("updated"));
            log.info("  - 跳过文件: {} 个", result.get("skipped"));
            log.info("  - 错误文件: {} 个", result.get("errors"));
            log.info("  - 总计处理: {} 个文件", result.get("total"));
            log.info("  - 检测到任务数: {} 个", result.get("detectedTasks"));
            log.info("  - 总耗时: {} 秒", elapsedSeconds);

            // 特别提示跳过的文件信息
            if ((Integer) result.get("skipped") > 0) {
                log.warn("跳过了 {} 个无法识别taskId的文件，这些文件将不会被同步", result.get("skipped"));
            }

            // 特别提示错误文件信息
            if ((Integer) result.get("errors") > 0) {
                log.error("处理过程中发生 {} 个错误，请检查日志了解详情", result.get("errors"));
            }

            // 成功完成提示
            if ((Integer) result.get("errors") == 0) {
                log.info("✓ 定时同步任务成功完成，所有文件均正常处理");
            } else {
                log.warn("⚠ 定时同步任务完成，但存在错误文件，请关注");
            }

        } catch (Exception e) {
            long elapsedTime = System.currentTimeMillis() - startTime;
            log.error("=== 定时MinIO同步任务异常 ===");
            log.error("异常信息: {}", e.getMessage());
            log.error("耗时: {} 毫秒", elapsedTime);
            log.error("异常详情: ", e);
        }

        log.info("=== 定时MinIO自动同步任务结束 ===");
    }

    /**
     * 创建默认同步配置
     */
    private Map<String, Object> createDefaultSyncConfig() {
        Map<String, Object> defaultConfig = new HashMap<>();
        Map<String, Double> defaultLocation = new HashMap<>();
        defaultLocation.put("latitude", 25.059399);
        defaultLocation.put("longitude", 102.878423);

        defaultConfig.put("defaultLocation", defaultLocation);
        defaultConfig.put("creator", "system");
        defaultConfig.put("updater", "system");
        defaultConfig.put("orgCode", "A01");
        defaultConfig.put("preferBucketInfo", true);

        // 同步模式配置 - 只同步能识别taskId的文件，跳过无法识别的文件
        defaultConfig.put("syncMode", "SKIP_UNKNOWN"); // 跳过未知任务模式
        // defaultConfig.put("syncMode", "AUTO_DETECT"); // 自动检测模式
        // defaultConfig.put("syncMode", "DEFAULT_TASK"); // 使用默认任务模式
        // defaultConfig.put("defaultTaskId", 9999999999999999L); // 默认任务ID（仅在DEFAULT_TASK模式下需要）

        return defaultConfig;
    }
}