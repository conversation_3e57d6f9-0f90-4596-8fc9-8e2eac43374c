package org.jeecg.modules.data.spatiotemporal.service.impl;

import io.minio.ListObjectsArgs;
import io.minio.MinioClient;
import io.minio.Result;
import io.minio.messages.Item;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.data.spatiotemporal.entity.FlightInspectionImage;
import org.jeecg.modules.data.spatiotemporal.mapper.FlightInspectionImageMapper;
import org.jeecg.modules.data.spatiotemporal.service.IMinioSyncService;
import org.jeecg.modules.flight.service.IFlightRecordsService;
import org.jeecg.modules.flight.entity.FlightRecords;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MinIO同步服务实现类
 *
 * <AUTHOR>
 * @date 2025-04-10
 */
@Slf4j
@Service
public class MinioSyncServiceImpl implements IMinioSyncService {

    private MinioClient minioClient;

    @Autowired
    @Qualifier("flightInspectionImageMapperPrimary")
    private FlightInspectionImageMapper flightInspectionImageMapper;

    @Autowired
    private IFlightRecordsService flightRecordsService;

    @Value("${jeecg.minio.bucketName:djicloudapi}")
    private String defaultBucketName;

    @Value("${jeecg.minio.minio_url:http://172.16.199.153:9000}")
    private String minioUrl;

    @Value("${jeecg.minio.minio_name:djiacc}")
    private String minioName;

    @Value("${jeecg.minio.minio_pass:di^jT2cD*2g}")
    private String minioPass;

    // 用于从路径中提取ID的正则表达式
    private static final Pattern TASK_ID_PATTERN = Pattern.compile("/([0-9]{10,})/");

    // 用于匹配新的wayline路径格式的正则表达式（标准化后，已去掉djicloudapi前缀）
    // 匹配格式：wayline/随机UUID/DJI_时间戳_任务ID/图片文件
    private static final Pattern WAYLINE_TASK_ID_PATTERN = Pattern.compile("wayline/[^/]+/[^_]+_[^_]+_[^_]+_([0-9]+)/");

    @PostConstruct
    public void init() {
        try {
            // 如果配置值为null或空，尝试使用默认值
            String endpoint = StringUtils.isBlank(minioUrl) ? "http://172.16.199.153:9000" : minioUrl;
            String accessKey = StringUtils.isBlank(minioName) ? "djiacc" : minioName;
            String secretKey = StringUtils.isBlank(minioPass) ? "di^jT2cD*2g" : minioPass;

            this.minioClient = MinioClient.builder()
                    .endpoint(endpoint)
                    .credentials(accessKey, secretKey)
                    .build();

            // 验证连接是否成功
            try {
                boolean bucketExists = this.minioClient.bucketExists(io.minio.BucketExistsArgs.builder()
                        .bucket(defaultBucketName)
                        .build());
            } catch (Exception e) {
                // 检查桶存在性失败但不影响客户端初始化
            }
        } catch (Exception e) {
            // MinioClient初始化失败, 不立即抛出异常，让应用可以继续启动
        }
    }

    /**
     * 同步MinIO桶中的图片到数据库
     *
     * @param bucketName 桶名称，可选，未提供则使用默认配置的桶
     * @param taskId 任务ID，可选，用于只同步特定任务ID文件夹下的图片
     * @return 同步结果，包含新增和更新的图片数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Integer> syncImagesFromMinio(String bucketName, String taskId) {
        // 调用带默认配置的方法，传入null表示使用默认值
        return syncImagesFromMinio(bucketName, taskId, null);
    }

    /**
     * 同步MinIO桶中的图片到数据库，支持默认配置
     *
     * @param bucketName 桶名称，可选，未提供则使用默认配置的桶
     * @param taskId 任务ID，可选，用于只同步特定任务ID文件夹下的图片；为空时自动识别所有任务
     * @param defaultConfig 默认配置，包含默认位置、创建人等信息
     * @return 同步结果，包含新增和更新的图片数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Integer> syncImagesFromMinio(String bucketName, String taskId, Map<String, Object> defaultConfig) {
        // 使用默认桶名如果未提供
        final String bucket = StringUtils.isNotBlank(bucketName) ? bucketName : defaultBucketName;

        // 初始化计数器
        AtomicInteger inserted = new AtomicInteger(0);
        AtomicInteger updated = new AtomicInteger(0);
        AtomicInteger skipped = new AtomicInteger(0);
        AtomicInteger errors = new AtomicInteger(0);

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 获取默认配置
        Map<String, Double> defaultLocation = defaultConfig != null ?
                (Map<String, Double>) defaultConfig.get("defaultLocation") : null;
        String defaultCreator = defaultConfig != null ?
                (String) defaultConfig.get("creator") : "system";
        String defaultUpdater = defaultConfig != null ?
                (String) defaultConfig.get("updater") : "system";
        String defaultOrgCode = defaultConfig != null ?
                (String) defaultConfig.get("orgCode") : "A01";
        Boolean preferBucketInfo = defaultConfig != null && defaultConfig.get("preferBucketInfo") != null ?
                (Boolean) defaultConfig.get("preferBucketInfo") : true;

        // 获取同步模式配置
        String syncMode = defaultConfig != null ?
                (String) defaultConfig.get("syncMode") : "AUTO_DETECT"; // AUTO_DETECT, DEFAULT_TASK, SKIP_UNKNOWN
        Long defaultTaskId = defaultConfig != null && defaultConfig.get("defaultTaskId") != null ?
                (Long) defaultConfig.get("defaultTaskId") : null;

        // 如果默认位置为空，设置一个默认值
        if (defaultLocation == null) {
            defaultLocation = new HashMap<>();
            defaultLocation.put("latitude", 25.059399);
            defaultLocation.put("longitude", 102.878423);
        }

        // 任务ID校验 - 如果提供了taskId则需要验证格式
        boolean hasTaskId = StringUtils.isNotBlank(taskId);

        // 校验任务ID格式 - 如果提供了taskId，必须是纯数字
        if (hasTaskId && !taskId.trim().matches("^\\d+$")) {
            throw new RuntimeException("任务ID格式无效，必须是纯数字格式，如：1920681769136152577");
        }

        try {
            // 检查MinioClient是否已初始化
            if (minioClient == null) {
                throw new RuntimeException("MinioClient未初始化，同步操作已中止");
            }

            // 检查桶是否存在，如果不存在则创建
            boolean bucketExists = false;
            try {
                bucketExists = minioClient.bucketExists(
                        io.minio.BucketExistsArgs.builder().bucket(bucket).build()
                );

                if (!bucketExists) {
                    minioClient.makeBucket(
                            io.minio.MakeBucketArgs.builder().bucket(bucket).build()
                    );
                    bucketExists = true;
                }
            } catch (Exception e) {
                throw new RuntimeException("无法访问MinIO桶: " + e.getMessage(), e);
            }

            if (!bucketExists) {
                throw new RuntimeException("MinIO桶不存在且无法创建，同步操作已中止");
            }

            // 添加数据库连接测试
            try {
                Long totalImagesCount = flightInspectionImageMapper.selectCount(null);
            } catch (Exception e) {
                throw new RuntimeException("数据库连接失败，同步操作已中止: " + e.getMessage(), e);
            }

            // 任务ID格式化和前缀处理
            String exactPrefix = null;
            String taskIdForValidation = null;
            List<String> waylinePrefixes = new ArrayList<>();

            // 如果提供了taskId，则使用它作为前缀；否则不使用前缀（同步所有图片）
            if (hasTaskId) {
                taskIdForValidation = taskId.trim();

                // 首先尝试传统路径格式
                exactPrefix = taskIdForValidation;
                if (!exactPrefix.endsWith("/")) {
                    exactPrefix = exactPrefix + "/";
                }

                // 同时查找wayline路径下包含该任务ID的文件夹
                waylinePrefixes = findWaylinePathsForTaskId(bucket, taskIdForValidation);
            }

            // 预检查：如果指定了任务ID，确保该任务文件夹存在
            if (hasTaskId) {
                boolean folderExists = false;
                String checkMessage = "";

                try {
                    // 首先检查传统路径格式
                    Iterable<Result<Item>> checkResults = minioClient.listObjects(
                            ListObjectsArgs.builder()
                                    .bucket(bucket)
                                    .prefix(exactPrefix)
                                    .maxKeys(1)
                                    .build());

                    // 尝试获取第一个对象确认文件夹存在
                    for (Result<Item> result : checkResults) {
                        folderExists = true;
                        checkMessage = "找到传统格式路径: " + exactPrefix;
                        break;
                    }

                    // 如果传统路径不存在，检查wayline路径
                    if (!folderExists && !waylinePrefixes.isEmpty()) {
                        folderExists = true; // 如果找到了wayline路径，则认为文件夹存在
                        checkMessage = "找到新格式路径: " + waylinePrefixes.toString();
                    }

                    System.out.println("任务ID " + taskId + " 路径检查结果: " + checkMessage);
                    System.out.println("waylinePrefixes数量: " + waylinePrefixes.size() + ", 内容: " + waylinePrefixes);

                    if (!folderExists) {
                        throw new RuntimeException("任务ID " + taskId + " 对应的文件夹在MinIO中不存在。");
                    }
                } catch (RuntimeException re) {
                    // 重新抛出运行时异常
                    throw re;
                } catch (Exception e) {
                    throw new RuntimeException("无法验证任务ID文件夹是否存在: " + e.getMessage(), e);
                }
            }

            // 收集所有需要处理的对象
            List<Result<Item>> allResults = new ArrayList<>();

            // 如果指定了任务ID，需要从多个路径收集对象
            if (hasTaskId) {
                // 1. 收集传统路径下的对象
                try {
                    Iterable<Result<Item>> traditionalResults = minioClient.listObjects(
                            ListObjectsArgs.builder()
                                    .bucket(bucket)
                                    .prefix(exactPrefix)
                                    .recursive(true)
                                    .build());

                    for (Result<Item> result : traditionalResults) {
                        allResults.add(result);
                    }
                } catch (Exception e) {
                    // 传统路径查询失败，记录但继续
                    System.out.println("查询传统路径失败: " + e.getMessage());
                }

                // 2. 收集wayline路径下的对象
                for (String waylinePrefix : waylinePrefixes) {
                    try {
                        Iterable<Result<Item>> waylineResults = minioClient.listObjects(
                                ListObjectsArgs.builder()
                                        .bucket(bucket)
                                        .prefix(waylinePrefix)
                                        .recursive(true)
                                        .build());

                        for (Result<Item> result : waylineResults) {
                            allResults.add(result);
                        }
                    } catch (Exception e) {
                        // wayline路径查询失败，记录但继续
                        System.out.println("查询wayline路径失败: " + waylinePrefix + ", " + e.getMessage());
                    }
                }
            } else {
                // 如果没有指定任务ID，列出所有对象
                Iterable<Result<Item>> results = minioClient.listObjects(
                        ListObjectsArgs.builder()
                                .bucket(bucket)
                                .recursive(true)
                                .build());

                for (Result<Item> result : results) {
                    allResults.add(result);
                }
            }

            // 遍历对象并处理
            int objectCount = 0; // 用于计算总对象数
            int matchedCount = 0; // 匹配前缀的对象数
            Map<String, Integer> taskImageCount = new HashMap<>(); // 统计每个任务的图片数量

            for (Result<Item> result : allResults) {
                objectCount++;
                String storagePath = null; // 将storagePath定义移到try块外面

                try {
                    Item item = result.get();

                    // 跳过目录对象
                    if (item.isDir()) {
                        continue;
                    }

                    String objectName = item.objectName();

                    // 如果指定了任务ID，验证对象是否属于该任务
                    if (hasTaskId && StringUtils.isNotBlank(taskIdForValidation)) {
                        // 从路径提取任务ID进行验证
                        String extractedTaskId = extractTaskId(objectName);
                        if (!taskIdForValidation.equals(extractedTaskId)) {
                            skipped.incrementAndGet();
                            continue;
                        }

                        // 通过验证，确认此对象属于正确的任务ID
                        matchedCount++;
                    } else if (!hasTaskId) {
                        // 如果没有指定任务ID，处理所有图片文件
                        matchedCount++;
                    }

                    // 只处理图片文件
                    if (!isImageFile(objectName)) {
                        skipped.incrementAndGet();
                        continue;
                    }

                    // 获取对象元数据
                    storagePath = standardizeStoragePath(objectName);
                    String imageName = extractFileName(objectName);
                    long size = item.size();
                    ZonedDateTime lastModified = item.lastModified();
                    Date captureTime = Date.from(lastModified.toInstant());

                    // 检查数据库中是否已存在相同路径的记录
                    FlightInspectionImage existingImage = findImageByStoragePath(storagePath);

                    if (existingImage != null) {
                        // 更新现有记录
                        existingImage.setImageName(imageName);
                        existingImage.setCaptureTime(captureTime);
                        existingImage.setUpdateTime(new Date());
                        existingImage.setUpdateBy(defaultUpdater);

                        // 根据路径尝试提取任务ID
                        String extractedTaskId = extractTaskId(storagePath);
                        if (StringUtils.isNotBlank(extractedTaskId)) {
                            try {
                                // 如果是数字格式，直接用作任务ID
                                if (extractedTaskId.matches("^\\d+$")) {
                                    existingImage.setTaskId(Long.parseLong(extractedTaskId));
                                } else {
                                    // 如果不是纯数字，尝试将其转换为数字的哈希码
                                    long hashTaskId = Math.abs((long)extractedTaskId.hashCode());
                                    existingImage.setTaskId(hashTaskId);
                                }
                            } catch (Exception e) {
                                // 处理任务ID异常
                            }
                        }

                        flightInspectionImageMapper.updateById(existingImage);
                        updated.incrementAndGet();
                    } else {
                        // 创建新记录
                        FlightInspectionImage newImage = new FlightInspectionImage();
                        newImage.setStoragePath(storagePath);
                        newImage.setImageName(imageName);
                        newImage.setCaptureTime(captureTime);
                        newImage.setIsAnnotated(0);
                        newImage.setCreateTime(new Date());
                        newImage.setCreateBy(defaultCreator);
                        newImage.setSysOrgCode(defaultOrgCode);

                        // 智能任务ID分配策略
                        Long assignedTaskId = null;
                        String extractedTaskId = extractTaskId(storagePath);

                        if (StringUtils.isNotBlank(extractedTaskId) && extractedTaskId.matches("^\\d+$")) {
                            // 情况1：从路径成功提取到有效的数字任务ID
                            assignedTaskId = Long.parseLong(extractedTaskId);
                            taskImageCount.put(extractedTaskId, taskImageCount.getOrDefault(extractedTaskId, 0) + 1);
                        } else if (hasTaskId) {
                            // 情况2：指定了任务ID参数，使用指定的任务ID
                            assignedTaskId = Long.parseLong(taskId);
                        } else {
                            // 情况3：无法提取任务ID且未指定任务ID，根据同步模式处理
                            switch (syncMode) {
                                case "DEFAULT_TASK":
                                    if (defaultTaskId != null) {
                                        assignedTaskId = defaultTaskId;
                                    } else {
                                        throw new RuntimeException("同步模式为DEFAULT_TASK但未提供defaultTaskId配置");
                                    }
                                    break;
                                case "SKIP_UNKNOWN":
                                    // 跳过无法识别任务ID的图片
                                    skipped.incrementAndGet();
                                    // 记录跳过的文件信息用于调试
                                    if (skipped.get() <= 10) { // 只记录前10个跳过的文件，避免日志过多
                                        System.out.println("跳过无法识别taskId的文件: " + storagePath);
                                    }
                                    continue;
                                case "AUTO_DETECT":
                                default:
                                    // 自动检测模式：为无法识别的图片创建一个通用任务ID
                                    assignedTaskId = generateUnknownTaskId(storagePath);
                                    break;
                            }
                        }

                        if (assignedTaskId != null) {
                            newImage.setTaskId(assignedTaskId);
                        } else {
                            // 最后的兜底策略
                            newImage.setTaskId(System.currentTimeMillis());
                        }

                        // 尝试从路径提取位置信息
                        boolean locationFound = false;
                        if (preferBucketInfo) {
                            try {
                                // 如果路径格式为 /经度_纬度/文件名.jpg，则尝试提取经纬度
                                String[] parts = storagePath.split("/");
                                if (parts.length >= 2) {
                                    String possibleCoords = parts[parts.length - 2];
                                    if (possibleCoords.contains("_")) {
                                        String[] coords = possibleCoords.split("_");
                                        if (coords.length == 2) {
                                            try {
                                                double lng = Double.parseDouble(coords[0]);
                                                double lat = Double.parseDouble(coords[1]);

                                                // 创建GeoJSON Point对象
                                                String geoJson = String.format(
                                                        "{\"type\":\"Point\",\"coordinates\":[%f,%f]}",
                                                        lng, lat
                                                );
                                                newImage.setLocation(geoJson);
                                                locationFound = true;
                                            } catch (NumberFormatException e) {
                                                // 不是经纬度格式，忽略
                                            }
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                // 提取位置信息失败
                            }
                        }

                        // 如果无法从路径提取位置信息，或者不优先使用桶信息，则使用默认位置
                        if (!locationFound) {
                            try {
                                // 使用默认位置创建GeoJSON
                                double lng = defaultLocation.get("longitude");
                                double lat = defaultLocation.get("latitude");

                                // 为了避免所有图片位置完全相同，添加随机浮动 (-0.0003 ~ 0.0003)
                                double randomOffset = (Math.random() - 0.5) * 0.0006;
                                lng += randomOffset;
                                lat += randomOffset;

                                String geoJson = String.format(
                                        "{\"type\":\"Point\",\"coordinates\":[%f,%f]}",
                                        lng, lat
                                );
                                newImage.setLocation(geoJson);
                            } catch (Exception e) {
                                // 设置默认位置信息失败
                            }
                        }
                        try {
                            flightInspectionImageMapper.insert(newImage);
                            inserted.incrementAndGet();
                        } catch (Exception e) {
                            errors.incrementAndGet();
                            log.warn("插入图片记录失败: {}, 错误: {}", storagePath, e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    errors.incrementAndGet();
                    log.warn("处理图片对象失败: {}, 错误: {}", storagePath, e.getMessage());
                }
            }

            // 计算总耗时
            long endTime = System.currentTimeMillis();
            long elapsedSeconds = (endTime - startTime) / 1000;
            int total = inserted.get() + updated.get() + skipped.get() + errors.get();

            // 返回处理结果之前，更新飞行记录的照片状态
            updateFlightRecordsPhotoStatus(hasTaskId, taskId, taskImageCount, inserted.get(), updated.get(), errors.get());

            // 返回处理结果
            Map<String, Integer> resultMap = new HashMap<>();
            resultMap.put("inserted", inserted.get());
            resultMap.put("updated", updated.get());
            resultMap.put("skipped", skipped.get());
            resultMap.put("errors", errors.get());
            resultMap.put("total", total);
            resultMap.put("objectCount", objectCount);
            resultMap.put("matchedCount", matchedCount); // 新增匹配前缀的统计
            resultMap.put("elapsedSeconds", (int)elapsedSeconds);
            resultMap.put("detectedTasks", taskImageCount.size()); // 检测到的任务数量
            return resultMap;
        } catch (Exception e) {
            throw new RuntimeException("同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新飞行记录的照片状态
     * 支持单个任务和多个任务的状态更新
     */
    private void updateFlightRecordsPhotoStatus(boolean hasTaskId, String taskId,
                                               Map<String, Integer> taskImageCount,
                                               int totalInserted, int totalUpdated, int totalErrors) {
        try {
            if (hasTaskId && taskId != null && taskId.matches("^\\d+$")) {
                // 单个任务的状态更新
                updateSingleTaskPhotoStatus(taskId, totalInserted, totalUpdated, totalErrors);
            } else if (!hasTaskId && !taskImageCount.isEmpty()) {
                // 多个任务的批量状态更新
                updateMultipleTasksPhotoStatus(taskImageCount, totalInserted, totalUpdated, totalErrors);
            }
        } catch (Exception e) {
            log.error("更新飞行记录照片状态时发生异常", e);
        }
    }

    /**
     * 更新单个任务的照片状态
     */
    private void updateSingleTaskPhotoStatus(String taskId, int inserted, int updated, int errors) {
        try {
            long taskIdLong = Long.parseLong(taskId);

            // 确定照片状态
            FlightRecords.PhotoStatusEnum status;
            if (errors > 0) {
                status = FlightRecords.PhotoStatusEnum.FAILED;
            } else if (inserted > 0 || updated > 0) {
                status = FlightRecords.PhotoStatusEnum.COMPLETED;
            } else {
                status = FlightRecords.PhotoStatusEnum.NOT_SYNCED;
            }

            // 计算照片总数量（新增 + 更新的数量）
            int totalPhotoNums = inserted + updated;

            log.info("准备更新飞行记录照片状态: taskId={}, status={}, 新增={}, 更新={}, 错误={}, 总数={}",
                    taskIdLong, status, inserted, updated, errors, totalPhotoNums);

            // 更新飞行记录的照片状态
            boolean updateResult = flightRecordsService.updatePhotoStatus(
                    taskIdLong,
                    status,
                    inserted,
                    updated,
                    errors,
                    totalPhotoNums > 0 ? totalPhotoNums : null);

            if (updateResult) {
                log.info("飞行记录照片状态更新成功: taskId={}, status={}", taskIdLong, status);
            } else {
                log.warn("飞行记录照片状态更新失败: taskId={}", taskIdLong);
            }
        } catch (NumberFormatException e) {
            log.warn("无法将任务ID转换为数字: {}", taskId, e);
        } catch (Exception e) {
            log.error("更新单个任务照片状态失败: taskId={}", taskId, e);
        }
    }

    /**
     * 批量更新多个任务的照片状态
     */
    private void updateMultipleTasksPhotoStatus(Map<String, Integer> taskImageCount,
                                               int totalInserted, int totalUpdated, int totalErrors) {
        log.info("开始批量更新 {} 个任务的飞行记录照片状态", taskImageCount.size());

        int successCount = 0;
        int failCount = 0;

        for (Map.Entry<String, Integer> entry : taskImageCount.entrySet()) {
            String taskIdStr = entry.getKey();
            Integer imageCount = entry.getValue();

            try {
                if (!taskIdStr.matches("^\\d+$")) {
                    log.warn("跳过无效的任务ID: {}", taskIdStr);
                    continue;
                }

                long taskIdLong = Long.parseLong(taskIdStr);

                // 对于批量同步，假设每个任务都是成功的（因为能检测到图片）
                FlightRecords.PhotoStatusEnum status = FlightRecords.PhotoStatusEnum.COMPLETED;

                log.debug("更新任务 {} 的照片状态，检测到图片数量: {}", taskIdLong, imageCount);

                // 更新飞行记录的照片状态
                boolean updateResult = flightRecordsService.updatePhotoStatus(
                        taskIdLong,
                        status,
                        imageCount, // 将检测到的图片数量作为新增数量
                        0,          // 批量同步时更新数量设为0
                        0,          // 批量同步时错误数量设为0
                        imageCount  // 照片总数量
                );

                if (updateResult) {
                    successCount++;
                    log.debug("任务 {} 照片状态更新成功", taskIdLong);
                } else {
                    failCount++;
                    log.warn("任务 {} 照片状态更新失败", taskIdLong);
                }
            } catch (Exception e) {
                failCount++;
                log.error("更新任务 {} 照片状态时发生异常", taskIdStr, e);
            }
        }

        log.info("批量更新飞行记录照片状态完成: 成功 {} 个，失败 {} 个", successCount, failCount);
    }

    /**
     * 获取MinIO桶中的图片总数
     *
     * @param bucketName 桶名称，可选，未提供则使用默认配置的桶
     * @param taskId 任务ID，可选，用于只统计特定任务ID文件夹下的图片
     * @return 图片总数
     */
    @Override
    public int getImageCountFromMinio(String bucketName, String taskId) {
        // 使用默认桶名如果未提供
        final String bucket = StringUtils.isNotBlank(bucketName) ? bucketName : defaultBucketName;
        AtomicInteger count = new AtomicInteger(0);

        try {
            // 列出桶中的对象
            ListObjectsArgs.Builder listArgsBuilder = ListObjectsArgs.builder()
                    .bucket(bucket)
                    .recursive(true);

            // 如果指定了任务ID，则使用前缀过滤；否则统计所有图片
            if (StringUtils.isNotBlank(taskId)) {
                listArgsBuilder.prefix(taskId); // 使用任务ID作为前缀
            }

            Iterable<Result<Item>> results = minioClient.listObjects(listArgsBuilder.build());

            // 计算图片数量
            results.forEach(result -> {
                try {
                    Item item = result.get();
                    if (!item.isDir() && isImageFile(item.objectName())) {
                        count.incrementAndGet();
                    }
                } catch (Exception e) {
                    // 获取MinIO对象信息失败
                }
            });

            return count.get();
        } catch (Exception e) {
            // 统计MinIO图片总数失败
            return 0;
        }
    }

    /**
     * 判断文件是否是图片
     */
    private boolean isImageFile(String filename) {
        if (StringUtils.isBlank(filename)) {
            return false;
        }

        String lowerFilename = filename.toLowerCase();
        return lowerFilename.endsWith(".jpg") ||
                lowerFilename.endsWith(".jpeg") ||
                lowerFilename.endsWith(".png") ||
                lowerFilename.endsWith(".gif") ||
                lowerFilename.endsWith(".bmp");
    }

    /**
     * 从路径中提取文件名
     */
    private String extractFileName(String path) {
        if (StringUtils.isBlank(path)) {
            return "";
        }

        int lastSeparator = path.lastIndexOf('/');
        return lastSeparator >= 0 ? path.substring(lastSeparator + 1) : path;
    }

    /**
     * 根据存储路径查找图片记录
     */
    private FlightInspectionImage findImageByStoragePath(String storagePath) {
        try {
            // 这里使用自定义的查询方法，你可能需要在Mapper中添加相应的方法
            // 或者使用MyBatis-Plus提供的条件构造器
            // 实现根据storagePath查询记录的功能
            return flightInspectionImageMapper.selectOne(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<FlightInspectionImage>()
                            .eq(FlightInspectionImage::getStoragePath, storagePath));
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从存储路径中提取任务ID
     * 支持多种路径格式：
     * 1. 旧格式："/1901834767149375490/1744165622242.jpg" 提取 "1901834767149375490"
     * 2. 新格式（标准化后）："/wayline/09fbdac8-254a-4f80-8d82-9637c919d2cc/DJI_202507230946_106_1947829504834605057/DJI_20250723094750_0001_T.jpeg" 提取 "1947829504834605057"
     */
    private String extractTaskId(String storagePath) {
        if (StringUtils.isBlank(storagePath)) {
            return "";
        }

        // 移除开头的斜杠
        String normalizedPath = storagePath;
        if (normalizedPath.startsWith("/")) {
            normalizedPath = normalizedPath.substring(1);
        }

        // 优先检查新的wayline路径格式（已经去掉djicloudapi前缀）
        if (normalizedPath.contains("wayline/")) {
            // 尝试手动解析wayline路径
            String[] parts = normalizedPath.split("/");

            if (parts.length >= 3 && "wayline".equals(parts[0])) {
                // wayline/随机UUID/DJI_时间戳_任务ID/图片文件 或 wayline/随机UUID/DJI_时间戳_任务ID
                // 根据实际路径结构：wayline/14d45692-a92b-4abe-b9d9-f23783d5aa93/DJI_202507091534_106_1942849415252844546/DJI_20250709153601_0001_T.jpeg
                String folderName = parts[2]; // DJI_202507091534_106_1942849415252844546

                if (folderName.contains("_")) {
                    String[] folderParts = folderName.split("_");

                    // 检查是否符合 DJI_时间戳_数字_任务ID 格式
                    if (folderParts.length >= 4) {
                        String lastPart = folderParts[folderParts.length - 1]; // 1942849415252844546

                        if (lastPart.matches("^\\d+$")) {
                            return lastPart;
                        }
                    }
                    // 也检查是否符合 DJI_时间戳_任务ID 格式（3部分）
                    else if (folderParts.length == 3) {
                        String lastPart = folderParts[2]; // 任务ID

                        if (lastPart.matches("^\\d+$")) {
                            return lastPart;
                        }
                    }
                }
            }
        }

        // 检查传统路径格式
        String[] parts = normalizedPath.split("/");
        if (parts.length >= 2) {
            // 第一部分就是文件夹名称，即任务ID
            String folderId = parts[0];
            if (folderId.matches("^\\d+$")) {
                return folderId;
            }
        }

        // 作为备用，使用之前的正则表达式匹配方法
        Matcher matcher = TASK_ID_PATTERN.matcher(storagePath);
        if (matcher.find()) {
            return matcher.group(1);
        }

        return "";
    }

    /**
     * 查找wayline路径下包含指定任务ID的文件夹路径
     *
     * @param bucket 桶名称
     * @param taskId 任务ID
     * @return 包含该任务ID的wayline路径列表
     */
    private List<String> findWaylinePathsForTaskId(String bucket, String taskId) {
        List<String> waylinePaths = new ArrayList<>();

        try {
            System.out.println("findWaylinePathsForTaskId - 开始查找，桶: " + bucket + ", 任务ID: " + taskId);

            // 检查MinioClient是否已初始化
            if (minioClient == null) {
                System.out.println("findWaylinePathsForTaskId - MinioClient未初始化");
                return waylinePaths;
            }

            // 测试桶是否存在
            try {
                boolean bucketExists = minioClient.bucketExists(
                        io.minio.BucketExistsArgs.builder().bucket(bucket).build()
                );
                System.out.println("findWaylinePathsForTaskId - 桶存在性检查: " + bucketExists);
                if (!bucketExists) {
                    System.out.println("findWaylinePathsForTaskId - 桶不存在: " + bucket);
                    return waylinePaths;
                }
            } catch (Exception e) {
                System.out.println("findWaylinePathsForTaskId - 桶存在性检查失败: " + e.getMessage());
                return waylinePaths;
            }

            // 先测试是否有任何对象
            try {
                Iterable<Result<Item>> testResults = minioClient.listObjects(
                        ListObjectsArgs.builder()
                                .bucket(bucket)
                                .maxKeys(5)
                                .build());

                int testCount = 0;
                for (Result<Item> result : testResults) {
                    try {
                        Item item = result.get();
                        System.out.println("findWaylinePathsForTaskId - 桶中的对象示例: " + item.objectName());
                        testCount++;
                        if (testCount >= 5) break;
                    } catch (Exception e) {
                        // 忽略
                    }
                }
                System.out.println("findWaylinePathsForTaskId - 桶中总共找到 " + testCount + " 个对象示例");
            } catch (Exception e) {
                System.out.println("findWaylinePathsForTaskId - 测试查询失败: " + e.getMessage());
            }

            // 列出djicloudapi/wayline/下的所有对象
            System.out.println("findWaylinePathsForTaskId - 准备查询MinIO，桶: " + bucket + ", 前缀: djicloudapi/wayline/");
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(bucket)
                            .prefix("djicloudapi/wayline/")
                            .recursive(true)
                            .build());
            System.out.println("findWaylinePathsForTaskId - MinIO查询完成，开始遍历结果");

            Set<String> processedPaths = new HashSet<>();
            int totalObjects = 0;
            int matchingObjects = 0;

            for (Result<Item> result : results) {
                try {
                    Item item = result.get();
                    if (item.isDir()) {
                        continue;
                    }

                    String objectName = item.objectName();
                    totalObjects++;

                    if (totalObjects <= 5) { // 只打印前5个对象作为示例
                        System.out.println("findWaylinePathsForTaskId - 检查对象: " + objectName);
                    }

                    // 检查是否包含目标任务ID
                    if (objectName.contains(taskId)) {
                        matchingObjects++;
                        System.out.println("findWaylinePathsForTaskId - 找到包含任务ID的对象: " + objectName);

                        // 先标准化路径，然后提取任务ID进行验证
                        String standardizedPath = standardizeStoragePath(objectName);
                        System.out.println("findWaylinePathsForTaskId - 标准化路径: " + standardizedPath);

                        String extractedTaskId = extractTaskId(standardizedPath);
                        System.out.println("findWaylinePathsForTaskId - 提取的任务ID: " + extractedTaskId + ", 目标任务ID: " + taskId);

                        if (taskId.equals(extractedTaskId)) {
                            // 提取到文件夹级别的路径
                            String folderPath = extractWaylineFolderPath(objectName, taskId);
                            System.out.println("findWaylinePathsForTaskId - 提取的文件夹路径: " + folderPath);

                            if (folderPath != null && !processedPaths.contains(folderPath)) {
                                waylinePaths.add(folderPath);
                                processedPaths.add(folderPath);
                                System.out.println("findWaylinePathsForTaskId - 添加wayline路径: " + folderPath);
                            }
                        }
                    }
                } catch (Exception e) {
                    // 忽略单个对象的错误，继续处理其他对象
                    System.out.println("findWaylinePathsForTaskId - 处理对象时出错: " + e.getMessage());
                }
            }

            System.out.println("findWaylinePathsForTaskId - 完成，总对象数: " + totalObjects + ", 匹配对象数: " + matchingObjects + ", 找到路径数: " + waylinePaths.size());
        } catch (Exception e) {
            // 查找wayline路径失败，记录日志但不抛出异常
            System.out.println("findWaylinePathsForTaskId - 查找失败: " + e.getMessage());
            e.printStackTrace();
        }

        return waylinePaths;
    }

    /**
     * 从wayline对象路径中提取到文件夹级别的路径
     * 例如：djicloudapi/wayline/uuid/DJI_xxx_taskId/image.jpg -> djicloudapi/wayline/uuid/DJI_xxx_taskId/
     */
    private String extractWaylineFolderPath(String objectName, String taskId) {
        if (!objectName.contains("djicloudapi/wayline/") || !objectName.contains(taskId)) {
            return null;
        }

        // 找到包含任务ID的文件夹部分
        String[] parts = objectName.split("/");
        if (parts.length >= 5 && "djicloudapi".equals(parts[0]) && "wayline".equals(parts[1])) {
            // djicloudapi/wayline/uuid/DJI_xxx_taskId/image.jpg
            String folderName = parts[3];
            if (folderName.endsWith(taskId)) {
                return parts[0] + "/" + parts[1] + "/" + parts[2] + "/" + parts[3] + "/";
            }
        }

        return null;
    }

    /**
     * 获取MinIO中的图片流
     *
     * @param storagePath 图片存储路径
     * @return 图片输入流
     */
    @Override
    public InputStream getImageStream(String storagePath) {
        if (StringUtils.isBlank(storagePath)) {
            return null;
        }

        try {
            // 检查MinioClient是否已初始化
            if (minioClient == null) {
                return null;
            }

            // 处理路径 - 移除开头的斜杠，因为MinIO不需要前导斜杠
            String objectPath = storagePath;
            if (objectPath.startsWith("/")) {
                objectPath = objectPath.substring(1);
            }

            // 从MinIO获取图片流
            return minioClient.getObject(
                    io.minio.GetObjectArgs.builder()
                            .bucket(defaultBucketName)
                            .object(objectPath)
                            .build()
            );
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 标准化存储路径，处理新旧两种格式
     * 传统格式: 任务ID/图片文件名 -> /任务ID/图片文件名
     * 新格式: djicloudapi/wayline/随机UUID/DJI_时间戳_任务ID/图片文件名 -> /wayline/随机UUID/DJI_时间戳_任务ID/图片文件名
     * @param path 原始路径
     * @return 标准化后的路径
     */
    private String standardizeStoragePath(String path) {
        if (StringUtils.isBlank(path)) {
            return path;
        }

        String normalizedPath = path;

        // 处理新格式：去掉 djicloudapi 前缀
        if (normalizedPath.startsWith("djicloudapi/wayline/")) {
            // djicloudapi/wayline/uuid/DJI_xxx_taskId/image.jpg -> wayline/uuid/DJI_xxx_taskId/image.jpg
            normalizedPath = normalizedPath.substring("djicloudapi/".length());
        } else if (normalizedPath.startsWith("/djicloudapi/wayline/")) {
            // /djicloudapi/wayline/uuid/DJI_xxx_taskId/image.jpg -> wayline/uuid/DJI_xxx_taskId/image.jpg
            normalizedPath = normalizedPath.substring("/djicloudapi/".length());
        }

        // 确保路径以斜杠开头
        if (!normalizedPath.startsWith("/")) {
            normalizedPath = "/" + normalizedPath;
        }

        return normalizedPath;
    }

    /**
     * 为无法识别任务ID的图片生成一个通用任务ID
     * 基于文件路径生成稳定的哈希值，确保相同路径的图片使用相同的任务ID
     */
    private Long generateUnknownTaskId(String storagePath) {
        if (StringUtils.isBlank(storagePath)) {
            return System.currentTimeMillis();
        }

        // 提取路径的父目录作为任务标识
        String parentPath = "";
        int lastSlash = storagePath.lastIndexOf('/');
        if (lastSlash > 0) {
            parentPath = storagePath.substring(0, lastSlash);
        }

        // 如果父目录为空或者是根目录，使用固定的未知任务ID
        if (StringUtils.isBlank(parentPath) || "/".equals(parentPath)) {
            return 9999999999999999L; // 固定的未知任务ID
        }

        // 基于父目录路径生成稳定的哈希值作为任务ID
        int hash = parentPath.hashCode();
        // 确保生成的ID是正数且足够大
        long taskId = Math.abs((long) hash) + 1000000000000000L;
        return taskId;
    }
}