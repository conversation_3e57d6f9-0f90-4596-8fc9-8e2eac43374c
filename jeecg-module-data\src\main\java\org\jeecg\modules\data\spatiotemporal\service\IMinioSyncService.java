package org.jeecg.modules.data.spatiotemporal.service;

import java.io.InputStream;
import java.util.Map;

/**
 * MinIO同步服务接口
 * 负责从MinIO对象存储同步图片元数据到数据库
 *
 * <AUTHOR>
 * @date 2025-04-10
 */
public interface IMinioSyncService {

    /**
     * 同步MinIO桶中的图片到数据库
     *
     * @param bucketName 桶名称，可选，未提供则使用默认配置的桶
     * @param taskId 任务ID，可选，用于只同步特定任务ID文件夹下的图片
     * @return 同步结果，包含新增和更新的图片数量
     */
    Map<String, Integer> syncImagesFromMinio(String bucketName, String taskId);

    /**
     * 同步MinIO桶中的图片到数据库，支持默认配置
     *
     * @param bucketName 桶名称，可选，未提供则使用默认配置的桶
     * @param taskId 任务ID，可选，用于只同步特定任务ID文件夹下的图片
     * @param defaultConfig 默认配置，包含默认位置、创建人等信息
     * @return 同步结果，包含新增和更新的图片数量
     */
    Map<String, Integer> syncImagesFromMinio(String bucketName, String taskId, Map<String, Object> defaultConfig);

    /**
     * 获取MinIO桶中的图片总数
     *
     * @param bucketName 桶名称，可选，未提供则使用默认配置的桶
     * @param taskId 任务ID，可选，用于只统计特定任务ID文件夹下的图片
     * @return 图片总数
     */
    int getImageCountFromMinio(String bucketName, String taskId);

    /**
     * 获取MinIO中的图片流
     *
     * @param storagePath 图片存储路径
     * @return 图片输入流
     */
    InputStream getImageStream(String storagePath);
}