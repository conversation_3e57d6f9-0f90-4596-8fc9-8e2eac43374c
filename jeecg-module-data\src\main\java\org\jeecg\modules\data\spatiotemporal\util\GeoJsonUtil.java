package org.jeecg.modules.data.spatiotemporal.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * GeoJSON工具类
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Slf4j
public class GeoJsonUtil {

    /**
     * 创建Point类型的GeoJSON
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return GeoJSON字符串
     */
    public static String createPointGeoJson(double longitude, double latitude) {
        Map<String, Object> geoJson = new HashMap<>(2);
        geoJson.put("type", "Point");
        
        // GeoJSON坐标格式为[经度, 纬度]
        double[] coordinates = new double[]{longitude, latitude};
        geoJson.put("coordinates", coordinates);
        
        return JSON.toJSONString(geoJson);
    }

    /**
     * 从GeoJSON中提取坐标
     *
     * @param geoJson GeoJSON字符串
     * @return 坐标数组[经度, 纬度]
     */
    public static double[] extractCoordinates(String geoJson) {
        if (StringUtils.isBlank(geoJson)) {
            return null;
        }
        
        try {
            JSONObject jsonObject = JSON.parseObject(geoJson);
            
            if (!"Point".equals(jsonObject.getString("type"))) {
                log.warn("不支持的GeoJSON类型: {}", jsonObject.getString("type"));
                return null;
            }
            
            return JSON.parseObject(jsonObject.getString("coordinates"), double[].class);
        } catch (Exception e) {
            log.error("解析GeoJSON失败", e);
            return null;
        }
    }

    /**
     * 验证GeoJSON是否有效
     *
     * @param geoJson GeoJSON字符串
     * @return 是否有效
     */
    public static boolean isValidGeoJson(String geoJson) {
        if (StringUtils.isBlank(geoJson)) {
            return false;
        }
        
        try {
            JSONObject jsonObject = JSON.parseObject(geoJson);
            
            if (!"Point".equals(jsonObject.getString("type"))) {
                return false;
            }
            
            double[] coordinates = JSON.parseObject(jsonObject.getString("coordinates"), double[].class);
            
            return coordinates != null && coordinates.length == 2;
        } catch (Exception e) {
            log.error("验证GeoJSON失败", e);
            return false;
        }
    }
} 