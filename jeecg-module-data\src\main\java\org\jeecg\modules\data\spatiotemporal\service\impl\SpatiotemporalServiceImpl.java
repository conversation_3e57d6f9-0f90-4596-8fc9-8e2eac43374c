package org.jeecg.modules.data.spatiotemporal.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.jeecg.modules.data.spatiotemporal.dto.SpatiotemporalQueryDTO;
import org.jeecg.modules.data.spatiotemporal.entity.FlightInspectionImage;
import org.jeecg.modules.data.spatiotemporal.mapper.FlightInspectionImageMapper;
import org.jeecg.modules.data.spatiotemporal.service.ISpatiotemporalService;

import org.jeecg.modules.data.spatiotemporal.util.TimeRangeUtil;
import org.jeecg.modules.data.spatiotemporal.vo.HeatPointVO;
import org.jeecg.modules.data.spatiotemporal.vo.HeatmapResponseVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 时空搜图服务实现类
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpatiotemporalServiceImpl implements ISpatiotemporalService {

    // 定义热力图颜色常量，与前端保持一致
    private static final Map<String, String> HEATMAP_COLORS = new HashMap<String, String>() {{
        put("1-5", "#ADD8E6");   // 淡蓝色
        put("6-10", "#98FB98");  // 浅绿色
        put("11-15", "#32CD32"); // 绿色
        put("16-20", "#FFA500"); // 橙色
        put("21-25", "#FF6347"); // 番茄红
        put("26+", "#B22222");   // 深红色
    }};

    // 默认的经纬度中心点
    @Value("${spatiotemporal.default.longitude:102.878487}")
    private Double defaultLongitude;

    @Value("${spatiotemporal.default.latitude:25.054841}")
    private Double defaultLatitude;



    @Autowired
    @Qualifier("flightInspectionImageMapperPrimary")
    private FlightInspectionImageMapper flightInspectionImageMapper;

    /**
     * 获取热力图数据
     *
     * @param queryDTO 查询参数
     * @return
     */
    @Override
    public HeatmapResponseVO getHeatmapData(SpatiotemporalQueryDTO queryDTO) {
        log.info("从数据库获取热力图数据，时间范围: {}, 开始日期: {}, 结束日期: {}",
                queryDTO.getTimeRange(), queryDTO.getStartDate(), queryDTO.getEndDate());

        // 构建响应对象
        HeatmapResponseVO response = new HeatmapResponseVO();
        List<HeatPointVO> heatPoints = new ArrayList<>();

        try {
            // 获取时间范围 - 支持直接传入的日期范围
            Map<String, String> timeRange = TimeRangeUtil.getTimeRange(
                    queryDTO.getTimeRange(),
                    queryDTO.getStartDate(),
                    queryDTO.getEndDate()
            );
            String startTime = timeRange.get("startTime");
            String endTime = timeRange.get("endTime");

            // 获取经纬度和半径
            Double longitude = queryDTO.getLongitude();
            Double latitude = queryDTO.getLatitude();
            Integer radius = queryDTO.getRadius();

            // 处理经纬度为null的情况，使用默认值
            if (longitude == null || latitude == null) {
                log.info("经纬度参数为null，尝试获取默认中心点");
                try {
                    // 尝试从配置获取默认中心点
                    Map<String, Object> defaultCenter = getInitialCenter();
                    if (defaultCenter != null && defaultCenter.containsKey("longitude") && defaultCenter.containsKey("latitude")) {
                        longitude = Double.parseDouble(String.valueOf(defaultCenter.get("longitude")));
                        latitude = Double.parseDouble(String.valueOf(defaultCenter.get("latitude")));
                        log.info("使用默认中心点: 经度={}, 纬度={}", longitude, latitude);
                    } else {
                        // 如果仍然无法获取，使用注入的默认值
                        longitude = defaultLongitude;
                        latitude = defaultLatitude;
                        log.info("无法获取默认中心点，使用配置默认值: 经度={}, 纬度={}", longitude, latitude);
                    }
                } catch (Exception e) {
                    // 如果获取默认中心点失败，使用注入的默认值
                    longitude = defaultLongitude;
                    latitude = defaultLatitude;
                    log.error("获取默认中心点异常，使用配置默认值: 经度={}, 纬度={}, 异常: {}", longitude, latitude, e.getMessage());
                }
            }

            // 如果半径为null或者小于等于0，设置一个较大的默认值，以确保能获取到数据
            if (radius == null || radius <= 0) {
                radius = 400000; // 默认400km
                log.info("半径参数为null或小于等于0，使用默认半径: {}米", radius);
            }

            // 安全地查询热力图数据
            try {
                heatPoints = flightInspectionImageMapper.getHeatmapData(
                        startTime, endTime, longitude, latitude, radius);

                log.info("查询到热力点数据: {}条", heatPoints.size());
            } catch (Exception e) {
                log.error("查询热力图数据异常: {}", e.getMessage(), e);
                // 发生异常时返回空列表，但不抛出异常中断流程
                heatPoints = new ArrayList<>();
            }

            // 如果数据为空，尝试重新查询，不使用空间筛选
            if (heatPoints.isEmpty() && radius > 0) {
                log.info("未找到热力点数据，尝试不使用空间筛选进行查询");
                try {
                    // 设置较大的半径或将半径设置为0，禁用空间筛选
                    heatPoints = flightInspectionImageMapper.getHeatmapData(
                            startTime, endTime, longitude, latitude, 0);
                    log.info("不使用空间筛选查询到热力点数据: {}条", heatPoints.size());
                } catch (Exception e) {
                    log.error("不使用空间筛选查询热力图数据异常: {}", e.getMessage(), e);
                }
            }

            // 设置热力图颜色 (使用统一的颜色配置)
            for (HeatPointVO point : heatPoints) {
                if (point == null || point.getCount() == null) {
                    continue;
                }
                int count = point.getCount();
                if (count <= 5) {
                    point.setColor(HEATMAP_COLORS.get("1-5")); // 1-5次：淡蓝色
                } else if (count <= 10) {
                    point.setColor(HEATMAP_COLORS.get("6-10")); // 6-10次：浅绿色
                } else if (count <= 15) {
                    point.setColor(HEATMAP_COLORS.get("11-15")); // 11-15次：绿色
                } else if (count <= 20) {
                    point.setColor(HEATMAP_COLORS.get("16-20")); // 16-20次：橙色
                } else if (count <= 25) {
                    point.setColor(HEATMAP_COLORS.get("21-25")); // 21-25次：番茄红
                } else {
                    point.setColor(HEATMAP_COLORS.get("26+")); // 26次以上：深红色
                }
            }

            // 构建响应
            response.setRecords(heatPoints);
            response.setTotal((long) heatPoints.size());
            response.setCurrent(1);
            response.setSize(heatPoints.size());
            response.setPages(1);

            // 计算最大热力值
            Integer maxCount = heatPoints.stream()
                    .filter(Objects::nonNull)
                    .map(HeatPointVO::getCount)
                    .filter(Objects::nonNull)
                    .max(Integer::compareTo)
                    .orElse(0);
            response.setMaxCount(maxCount);

        } catch (Exception e) {
            // 捕获所有异常，确保即使出错也返回一个有效的响应对象
            log.error("处理热力图数据异常: {}", e.getMessage(), e);
            response.setRecords(new ArrayList<>());
            response.setTotal(0L);
            response.setCurrent(1);
            response.setSize(0);
            response.setPages(1);
            response.setMaxCount(0);
        }

        return response;
    }

    /**
     * 获取初始中心点 - 添加缓存，因为变化不频繁
     *
     * @return 中心点坐标
     */
    @Override
    public Map<String, Object> getInitialCenter() {
        log.info("从数据库获取初始中心点");
        Map<String, Object> center = new HashMap<>(2);

        // 获取数据库中的中心点
        try {
            // 直接使用简单查询，避免使用索引出错
            String sql = "SELECT " +
                    "JSON_EXTRACT(location, '$.coordinates[0]') AS longitude, " +
                    "JSON_EXTRACT(location, '$.coordinates[1]') AS latitude " +
                    "FROM flight_inspection_images " +
                    "WHERE location IS NOT NULL " +
                    "AND JSON_EXTRACT(location, '$.coordinates[0]') IS NOT NULL " +
                    "AND JSON_EXTRACT(location, '$.coordinates[1]') IS NOT NULL " +
                    "LIMIT 1";

            // 使用Mapper接口查询
            try {
                Map<String, Map<String, Object>> centerMap = flightInspectionImageMapper.getInitialCenter();

                // 如果数据库中有记录，则使用第一条记录的中心点
                if (centerMap != null && !centerMap.isEmpty() && centerMap.containsKey("center")) {
                    Map<String, Object> centerData = centerMap.get("center");
                    if (centerData != null && centerData.containsKey("longitude") && centerData.containsKey("latitude")) {
                        center.put("longitude", centerData.get("longitude"));
                        center.put("latitude", centerData.get("latitude"));
                        log.info("成功获取数据库中的中心点: {}", center);
                        return center;
                    }
                }
            } catch (Exception e) {
                log.error("使用Mapper接口获取中心点异常: {}", e.getMessage());
                // 发生异常时继续执行，尝试其他方法
            }

            // 如果无法从数据库获取有效的中心点，使用默认值
            log.warn("数据库中没有找到有效的中心点，使用默认中心点");
            center.put("longitude", 102.878487); // 默认经度
            center.put("latitude", 25.054841);   // 默认纬度
            return center;
        } catch (Exception e) {
            log.error("获取中心点异常，使用默认值: {}", e.getMessage(), e);
            center.put("longitude", 102.878487); // 默认经度
            center.put("latitude", 25.054841);   // 默认纬度
            return center;
        }
    }

    /**
     * 获取热力图颜色配置 - 添加缓存，因为配置是固定的
     *
     * @return 颜色配置
     */
    @Override
    public Map<String, String> getHeatmapColorConfig() {
        log.info("获取热力图颜色配置");
        // 直接返回常量，确保一致性
        return new HashMap<>(HEATMAP_COLORS);
    }

    /**
     * 统一的图片查询方法
     * 整合了原queryImagesByLocation和queryImagesByLocationEnhanced功能
     *
     * @param queryDTO 查询参数
     * @param needPaging 是否需要分页
     * @param sortByCaptureTimeAsc 是否按拍摄时间升序排列
     * @return 查询结果
     */
    @Override
    public Page<FlightInspectionImage> queryImages(SpatiotemporalQueryDTO queryDTO, Boolean needPaging, Boolean sortByCaptureTimeAsc) {
        log.info("统一图片查询，经度: {}, 纬度: {}, 半径: {}, 是否分页: {}, 是否按拍摄时间升序: {}",
                queryDTO.getLongitude(), queryDTO.getLatitude(), queryDTO.getRadius(), needPaging, sortByCaptureTimeAsc);

        // 获取时间范围 - 支持直接传入的日期范围
        Map<String, String> timeRange = TimeRangeUtil.getTimeRange(
                queryDTO.getTimeRange(),
                queryDTO.getStartDate(),
                queryDTO.getEndDate()
        );
        String startTime = timeRange.get("startTime");
        String endTime = timeRange.get("endTime");

        // 确保半径参数有效，默认为200米
        Integer radius = queryDTO.getRadius();
        if (radius == null || radius <= 0) {
            radius = 200;
            queryDTO.setRadius(radius);
            log.info("半径参数无效，使用默认值: {}米", radius);
        }

        try {
            // 处理分页设置
            Page<FlightInspectionImage> page;
            if (Boolean.TRUE.equals(needPaging)) {
                // 使用请求中的分页参数
                page = new Page<>(queryDTO.getCurrent(), queryDTO.getPageSize());
            } else {
                // 不需要分页，设置一个很大的size
                page = new Page<>(1, 10000);
            }

            // 设置默认排序，如果未指定
            if (sortByCaptureTimeAsc == null) {
                sortByCaptureTimeAsc = true;
            }

            // 调用Mapper方法执行查询
            return flightInspectionImageMapper.queryImages(
                    page,
                    queryDTO.getLongitude(),
                    queryDTO.getLatitude(),
                    queryDTO.getRadius(),
                    startTime,
                    endTime,
                    needPaging,
                    sortByCaptureTimeAsc
            );
        } catch (Exception e) {
            log.error("统一图片查询异常: {}", e.getMessage(), e);
            // 发生异常时返回空分页对象
            return new Page<>();
        }
    }



    /**
     * 批量删除图片
     *
     * @param ids 图片ID集合，以逗号分隔
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteImages(String ids) {
        if (StringUtils.isBlank(ids)) {
            return false;
        }

        String[] idList = ids.split(",");
        log.info("开始批量删除图片，ID数量: {}", idList.length);



        // 执行数据库删除
        try {
            // 批量删除图片记录
            int deletedCount = flightInspectionImageMapper.deleteBatchIds(Arrays.asList(idList));
            log.info("数据库删除成功，删除记录数: {}", deletedCount);



            return true;
        } catch (Exception e) {
            log.error("批量删除图片异常", e);
            throw e;
        }
    }









    /**
     * 根据ID获取图片
     *
     * @param imageId 图片ID
     * @return 图片信息
     */
    @Override
    public FlightInspectionImage getImageById(Long imageId) {
        if (imageId == null) {
            return null;
        }

        try {
            return flightInspectionImageMapper.selectById(imageId);
        } catch (Exception e) {
            log.error("根据ID获取图片异常", e);
            return null;
        }
    }

    /**
     * 获取数据库中图片总数
     *
     * @return 图片总数
     */
    @Override
    public int getTotalImagesCount() {
        try {
            // 使用MyBatis-Plus提供的selectCount方法获取总数
            Long count = flightInspectionImageMapper.selectCount(null);
            return count != null ? count.intValue() : 0;
        } catch (Exception e) {
            log.error("获取图片总数异常: {}", e.getMessage(), e);
            return 0;
        }
    }
}