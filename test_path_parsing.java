// 测试路径解析逻辑
public class TestPathParsing {
    
    public static void main(String[] args) {
        // 测试实际的路径
        String originalPath = "djicloudapi/wayline/14d45692-a92b-4abe-b9d9-f23783d5aa93/DJI_202507091534_106_1942849415252844546/DJI_20250709153601_0001_T.jpeg";
        
        System.out.println("原始路径: " + originalPath);
        
        // 模拟standardizeStoragePath方法
        String normalizedPath = originalPath;
        if (normalizedPath.startsWith("djicloudapi/wayline/")) {
            normalizedPath = normalizedPath.substring("djicloudapi/".length());
        }
        if (!normalizedPath.startsWith("/")) {
            normalizedPath = "/" + normalizedPath;
        }
        
        System.out.println("标准化路径: " + normalizedPath);
        
        // 模拟extractTaskId方法
        String pathForExtraction = normalizedPath;
        if (pathForExtraction.startsWith("/")) {
            pathForExtraction = pathForExtraction.substring(1);
        }
        
        System.out.println("用于提取的路径: " + pathForExtraction);
        
        if (pathForExtraction.contains("wayline/")) {
            String[] parts = pathForExtraction.split("/");
            System.out.println("路径分割结果: " + java.util.Arrays.toString(parts));
            
            if (parts.length >= 3 && "wayline".equals(parts[0])) {
                String folderName = parts[2];
                System.out.println("文件夹名称: " + folderName);
                
                if (folderName.contains("_")) {
                    String[] folderParts = folderName.split("_");
                    System.out.println("文件夹名称分割结果: " + java.util.Arrays.toString(folderParts));
                    
                    if (folderParts.length >= 4) {
                        String lastPart = folderParts[folderParts.length - 1];
                        System.out.println("最后一部分: " + lastPart);
                        
                        if (lastPart.matches("^\\d+$")) {
                            System.out.println("成功提取任务ID: " + lastPart);
                        } else {
                            System.out.println("最后一部分不是纯数字");
                        }
                    } else {
                        System.out.println("文件夹名称分割后少于4部分");
                    }
                } else {
                    System.out.println("文件夹名称不包含下划线");
                }
            } else {
                System.out.println("路径格式不匹配wayline格式");
            }
        } else {
            System.out.println("路径不包含wayline/");
        }
    }
}
