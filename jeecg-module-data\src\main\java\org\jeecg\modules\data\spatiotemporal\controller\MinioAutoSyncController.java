package org.jeecg.modules.data.spatiotemporal.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.data.spatiotemporal.service.IMinioSyncService;
import org.jeecg.modules.flight.service.IFlightRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * MinIO自动同步管理控制器
 * 提供手动触发同步、查看同步状态等功能
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Api(tags = "MinIO自动同步管理")
@RestController
@RequestMapping("/data/minioAutoSync")
@Slf4j
public class MinioAutoSyncController {

    @Autowired
    private IMinioSyncService minioSyncService;

    @Autowired
    private IFlightRecordsService flightRecordsService;

    /**
     * 手动触发全量自动同步
     * 同步所有能识别taskId的文件，跳过无法识别的文件
     */
    @AutoLog(value = "MinIO自动同步-手动触发全量同步")
    @ApiOperation(value = "手动触发全量自动同步", notes = "同步所有能识别taskId的文件，跳过无法识别的文件，并更新飞行记录照片状态")
    @PostMapping(value = "/triggerFullSync")
    public Result<Map<String, Object>> triggerFullSync() {
        log.info("手动触发MinIO全量自动同步...");
        try {
            // 设置默认配置
            Map<String, Object> defaultConfig = createDefaultConfig();

            // 同步模式配置 - 只同步能识别taskId的文件，跳过无法识别的文件
            defaultConfig.put("syncMode", "SKIP_UNKNOWN");

            // 执行智能批量同步（taskId=null表示同步所有图片）
            Map<String, Integer> resultInt = minioSyncService.syncImagesFromMinio(null, null, defaultConfig);
            Map<String, Object> result = new HashMap<>();
            resultInt.forEach((key, value) -> result.put(key, value));

            // 记录详细结果
            log.info("手动全量同步完成，结果: 新增={}, 更新={}, 跳过={}, 错误={}, 总计={}, 检测到任务数={}",
                    result.get("inserted"), result.get("updated"), result.get("skipped"),
                    result.get("errors"), result.get("total"), result.get("detectedTasks"));

            // 构建返回消息
            String message = String.format("同步完成！新增: %d, 更新: %d, 跳过: %d, 错误: %d, 检测到任务数: %d",
                    result.get("inserted"), result.get("updated"), result.get("skipped"),
                    result.get("errors"), result.get("detectedTasks"));

            return Result.OK(message, result);
        } catch (Exception e) {
            log.error("手动MinIO全量同步异常", e);
            return Result.error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发指定任务的同步
     *
     * @param taskId 任务ID
     */
    @AutoLog(value = "MinIO自动同步-手动触发指定任务同步")
    @ApiOperation(value = "手动触发指定任务同步", notes = "同步指定任务ID的文件，并更新飞行记录照片状态")
    @PostMapping(value = "/triggerTaskSync")
    public Result<Map<String, Object>> triggerTaskSync(@RequestParam("taskId") String taskId) {
        log.info("手动触发MinIO任务同步，taskId: {}", taskId);

        if (taskId == null || taskId.trim().isEmpty()) {
            return Result.error("任务ID不能为空");
        }

        if (!taskId.matches("^\\d+$")) {
            return Result.error("任务ID格式无效，必须是纯数字");
        }

        try {
            // 设置默认配置
            Map<String, Object> defaultConfig = createDefaultConfig();

            // 执行指定任务同步
            Map<String, Integer> resultInt = minioSyncService.syncImagesFromMinio(null, taskId.trim(), defaultConfig);
            Map<String, Object> result = new HashMap<>();
            resultInt.forEach((key, value) -> result.put(key, value));

            // 记录详细结果
            log.info("手动任务同步完成，taskId: {}, 结果: 新增={}, 更新={}, 跳过={}, 错误={}",
                    taskId, result.get("inserted"), result.get("updated"), result.get("skipped"), result.get("errors"));

            // 构建返回消息
            String message = String.format("任务 %s 同步完成！新增: %d, 更新: %d, 跳过: %d, 错误: %d",
                    taskId, result.get("inserted"), result.get("updated"), result.get("skipped"), result.get("errors"));

            return Result.OK(message, result);
        } catch (Exception e) {
            log.error("手动MinIO任务同步异常，taskId: {}", taskId, e);
            return Result.error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 获取MinIO桶中的图片统计信息
     *
     * @param taskId 任务ID（可选）
     */
    @AutoLog(value = "MinIO自动同步-获取图片统计")
    @ApiOperation(value = "获取MinIO图片统计", notes = "获取MinIO桶中的图片数量统计")
    @GetMapping(value = "/getImageStats")
    public Result<Map<String, Object>> getImageStats(@RequestParam(value = "taskId", required = false) String taskId) {
        log.info("获取MinIO图片统计，taskId: {}", taskId);
        try {
            int imageCount = minioSyncService.getImageCountFromMinio(null, taskId);

            Map<String, Object> result = new HashMap<>();
            result.put("imageCount", imageCount);
            result.put("taskId", taskId);

            String message = taskId != null ?
                    String.format("任务 %s 的图片数量: %d", taskId, imageCount) :
                    String.format("MinIO桶中总图片数量: %d", imageCount);

            return Result.OK(message, result);
        } catch (Exception e) {
            log.error("获取MinIO图片统计异常，taskId: {}", taskId, e);
            return Result.error("获取统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取同步状态信息
     */
    @AutoLog(value = "MinIO自动同步-获取同步状态")
    @ApiOperation(value = "获取同步状态", notes = "获取当前同步任务的状态信息")
    @GetMapping(value = "/getSyncStatus")
    public Result<Map<String, Object>> getSyncStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("schedulerEnabled", true);
            status.put("lastSyncTime", "定时任务运行中");
            status.put("nextSyncTime", "每天14:50");
            status.put("syncMode", "SKIP_UNKNOWN");
            status.put("description", "自动同步已启用，每天14:50执行，跳过无法识别taskId的文件");

            return Result.OK("获取同步状态成功", status);
        } catch (Exception e) {
            log.error("获取同步状态异常", e);
            return Result.error("获取状态失败: " + e.getMessage());
        }
    }

    /**
     * 创建默认配置
     */
    private Map<String, Object> createDefaultConfig() {
        Map<String, Object> defaultConfig = new HashMap<>();
        Map<String, Double> defaultLocation = new HashMap<>();
        defaultLocation.put("latitude", 25.059399);
        defaultLocation.put("longitude", 102.878423);

        defaultConfig.put("defaultLocation", defaultLocation);
        defaultConfig.put("creator", "system");
        defaultConfig.put("updater", "system");
        defaultConfig.put("orgCode", "A01");
        defaultConfig.put("preferBucketInfo", true);

        return defaultConfig;
    }
}
