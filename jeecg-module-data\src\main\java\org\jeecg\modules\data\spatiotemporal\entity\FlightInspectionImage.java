package org.jeecg.modules.data.spatiotemporal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 航拍图片信息实体类
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
@TableName("flight_inspection_images")
public class FlightInspectionImage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 拍摄时间
     */
    @NotNull(message = "拍摄时间不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date captureTime;

    /**
     * 图片名称
     */
    @NotBlank(message = "图片名称不能为空")
    private String imageName;

    /**
     * 高度
     */
    private Double altitude;

    /**
     * 分辨率宽度
     */
    private Integer resolutionWidth;

    /**
     * 分辨率高度
     */
    private Integer resolutionHeight;

    /**
     * 存储路径
     */
    @NotBlank(message = "存储路径不能为空")
    private String storagePath;

    /**
     * 图片完整URL
     */
    @TableField(exist = false)
    private String imageUrl;

    /**
     * 是否已标注（0:未标注，1:已标注）
     */
    @Dict(dicCode = "is_annotated")
    private Integer isAnnotated;

    /**
     * 地理位置（GeoJSON格式）
     */
    private String location;

    /**
     * 标注ID
     */
    private Long annotatedId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 所属部门编码
     */
    private String sysOrgCode;

    /**
     * 图片二进制数据
     * 在JSON序列化时忽略该字段，避免数据量过大
     * 使用transient确保不会被持久化到数据库
     */
    @JsonIgnore
    @TableField(exist = false)
    private transient byte[] imageData;

    /**
     * 缩略图二进制数据
     * 在JSON序列化时忽略该字段，避免数据量过大
     * 使用transient确保不会被持久化到数据库
     */
    @JsonIgnore
    @TableField(exist = false)
    private transient byte[] thumbnailData;
} 