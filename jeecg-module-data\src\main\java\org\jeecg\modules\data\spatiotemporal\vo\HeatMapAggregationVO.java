package org.jeecg.modules.data.spatiotemporal.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 热力图聚合点VO
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
public class HeatMapAggregationVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 网格经度
     */
    private Double gridLng;

    /**
     * 网格纬度
     */
    private Double gridLat;

    /**
     * 点位数量
     */
    private Integer count;

    /**
     * 颜色代码
     */
    private String color;
} 