<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.data.spatiotemporal.mapper.FlightInspectionImageMapper">

    <!-- 获取热力图数据：优化Query -->
    <select id="getHeatmapData" resultType="org.jeecg.modules.data.spatiotemporal.vo.HeatPointVO">
        SELECT
        CAST(JSON_EXTRACT(location, '$.coordinates[0]') AS DECIMAL(10,6)) as longitude,
        CAST(JSON_EXTRACT(location, '$.coordinates[1]') AS DECIMAL(10,6)) as latitude,
        count(*) as count,
        MAX(capture_time) as captureTime
        FROM flight_inspection_images
        WHERE location IS NOT NULL
        AND JSON_EXTRACT(location, '$.coordinates[0]') IS NOT NULL
        AND JSON_EXTRACT(location, '$.coordinates[1]') IS NOT NULL
        <if test="timeRangeStart != null and timeRangeEnd != null">
            AND capture_time BETWEEN #{timeRangeStart} AND #{timeRangeEnd}
        </if>
        <if test="lng != null and lat != null and radius != null and radius > 0">
            /* 只有当半径>0时才进行空间筛选 */
            AND (
            6371000 * acos(
            cos(radians(#{lat})) *
            cos(radians(CAST(JSON_EXTRACT(location, '$.coordinates[1]') AS DECIMAL(10,6)))) *
            cos(radians(CAST(JSON_EXTRACT(location, '$.coordinates[0]') AS DECIMAL(10,6))) - radians(#{lng})) +
            sin(radians(#{lat})) *
            sin(radians(CAST(JSON_EXTRACT(location, '$.coordinates[1]') AS DECIMAL(10,6))))
            ) &lt; #{radius}
            )
        </if>
        GROUP BY CAST(JSON_EXTRACT(location, '$.coordinates[0]') AS DECIMAL(10,6)),
        CAST(JSON_EXTRACT(location, '$.coordinates[1]') AS DECIMAL(10,6))
        ORDER BY count DESC
        LIMIT 5000
    </select>

    <!-- 获取系统初始中心点：优化Query -->
    <select id="getInitialCenter" resultType="java.util.Map">
        SELECT
            'center' as `key`,
            CAST(JSON_EXTRACT(location, '$.coordinates[0]') AS DECIMAL(10,6)) as longitude,
            CAST(JSON_EXTRACT(location, '$.coordinates[1]') AS DECIMAL(10,6)) as latitude
        FROM flight_inspection_images
        WHERE location IS NOT NULL
          AND JSON_EXTRACT(location, '$.coordinates[0]') IS NOT NULL
          AND JSON_EXTRACT(location, '$.coordinates[1]') IS NOT NULL
            LIMIT 1
    </select>

    <!-- 统一的图片查询方法：整合了所有位置查询功能 -->
    <select id="queryImages" resultType="org.jeecg.modules.data.spatiotemporal.entity.FlightInspectionImage">
        SELECT
        id, task_id, image_name, storage_path, location, capture_time,
        altitude, resolution_width, resolution_height, is_annotated,
        annotated_id, create_by, create_time, update_by, update_time,
        sys_org_code, location_lat, location_lon
        FROM flight_inspection_images
        WHERE location IS NOT NULL
        <if test="lng != null and lat != null and radius != null and radius > 0">
            /* 只有当经纬度和半径都有效时才进行空间筛选 */
            AND (
            6371000 * acos(
            cos(radians(#{lat})) *
            cos(radians(CAST(JSON_EXTRACT(location, '$.coordinates[1]') AS DECIMAL(10,6)))) *
            cos(radians(CAST(JSON_EXTRACT(location, '$.coordinates[0]') AS DECIMAL(10,6))) - radians(#{lng})) +
            sin(radians(#{lat})) *
            sin(radians(CAST(JSON_EXTRACT(location, '$.coordinates[1]') AS DECIMAL(10,6))))
            ) &lt; #{radius}
            )
        </if>
        <if test="timeRangeStart != null and timeRangeEnd != null">
            AND capture_time BETWEEN #{timeRangeStart} AND #{timeRangeEnd}
        </if>
        /* 根据sortByCaptureTimeAsc参数决定排序方式 */
        <choose>
            <when test="sortByCaptureTimeAsc != null and sortByCaptureTimeAsc">
                ORDER BY capture_time ASC
            </when>
            <otherwise>
                ORDER BY capture_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 批量删除图片 -->
    <delete id="batchDeleteByIds">
        DELETE FROM flight_inspection_images
        WHERE id IN
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 更新图片标记状态 -->
    <update id="updateImageMarkStatus">
        UPDATE flight_inspection_images
        SET is_annotated = #{isAnnotated},
            update_by = #{updateBy},
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>