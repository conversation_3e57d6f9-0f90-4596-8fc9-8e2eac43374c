package org.jeecg.modules.data.spatiotemporal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 热力图点位VO
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
public class HeatPointVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 热力值(点位数量)
     */
    private Integer count;

    /**
     * 颜色(可选)
     */
    private String color;

    /**
     * 拍摄时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date captureTime;
} 