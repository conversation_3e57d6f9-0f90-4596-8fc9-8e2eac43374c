package org.jeecg.modules.data.spatiotemporal.util;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.Map;

/**
 * 时间范围工具类
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
public class TimeRangeUtil {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 获取时间范围
     *
     * @param timeRangeType 时间范围类型或日期范围(yyyy-MM-dd,yyyy-MM-dd)
     * @return 开始时间和结束时间
     */
    public static Map<String, String> getTimeRange(String timeRangeType) {
        Map<String, String> result = new HashMap<>(2);

        // 先检查是否为日期范围格式
        if (StringUtils.isNotBlank(timeRangeType) && timeRangeType.contains(",")) {
            try {
                String[] dates = timeRangeType.split(",");
                if (dates.length >= 1) {
                    LocalDateTime startTime = LocalDateTime.of(
                            LocalDate.parse(dates[0], DATE_FORMATTER),
                            LocalTime.MIN
                    );

                    LocalDateTime endTime;
                    if (dates.length >= 2) {
                        endTime = LocalDateTime.of(
                                LocalDate.parse(dates[1], DATE_FORMATTER),
                                LocalTime.MAX
                        );
                    } else {
                        // 如果只有一个日期，则开始和结束日期相同
                        endTime = LocalDateTime.of(
                                LocalDate.parse(dates[0], DATE_FORMATTER),
                                LocalTime.MAX
                        );
                    }

                    // 确保开始时间不晚于结束时间
                    if (startTime.isAfter(endTime)) {
                        LocalDateTime temp = startTime;
                        startTime = endTime;
                        endTime = temp;
                    }

                    result.put("startTime", startTime.format(DATE_TIME_FORMATTER));
                    result.put("endTime", endTime.format(DATE_TIME_FORMATTER));
                    return result;
                }
            } catch (DateTimeParseException e) {
                // 日期解析错误，继续按常规类型处理
            }
        }

        // 按常规类型处理
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime;
        LocalDateTime endTime = now;

        // 若传入的timeRangeType为空，默认使用current_month
        if (StringUtils.isBlank(timeRangeType)) {
            timeRangeType = "current_month";
        }

        switch (timeRangeType) {
            case "current_month":
                // 本月
                startTime = LocalDateTime.of(
                        LocalDate.of(now.getYear(), now.getMonth(), 1),
                        LocalTime.MIN
                );
                break;
            case "two_months":
                // 近两月
                startTime = LocalDateTime.of(
                        LocalDate.of(now.getYear(), now.getMonth().minus(1), 1),
                        LocalTime.MIN
                );
                break;
            case "three_months":
                // 近三月
                startTime = LocalDateTime.of(
                        LocalDate.of(now.getYear(), now.getMonth().minus(2), 1),
                        LocalTime.MIN
                );
                break;
            case "half_year":
                // 半年
                startTime = LocalDateTime.of(
                        LocalDate.of(now.getYear(), now.getMonth().minus(5), 1),
                        LocalTime.MIN
                );
                break;
            default:
                // 默认本月
                startTime = LocalDateTime.of(
                        LocalDate.of(now.getYear(), now.getMonth(), 1),
                        LocalTime.MIN
                );
        }

        result.put("startTime", startTime.format(DATE_TIME_FORMATTER));
        result.put("endTime", endTime.format(DATE_TIME_FORMATTER));

        return result;
    }

    /**
     * 获取时间范围 - 支持直接传入开始和结束日期
     *
     * @param timeRangeType 时间范围类型或日期范围(yyyy-MM-dd,yyyy-MM-dd)
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate 结束日期 (yyyy-MM-dd)
     * @return 开始时间和结束时间 (yyyy-MM-dd HH:mm:ss)
     */
    public static Map<String, String> getTimeRange(String timeRangeType, String startDate, String endDate) {
        // 当开始日期和结束日期都有效时，优先使用传入的日期
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            Map<String, String> result = new HashMap<>(2);

            try {
                // 解析日期并设置时间范围
                LocalDateTime startTime = LocalDateTime.of(
                        LocalDate.parse(startDate, DATE_FORMATTER),
                        LocalTime.MIN
                );

                LocalDateTime endTime = LocalDateTime.of(
                        LocalDate.parse(endDate, DATE_FORMATTER),
                        LocalTime.MAX
                );

                // 确保开始时间不晚于结束时间
                if (startTime.isAfter(endTime)) {
                    LocalDateTime temp = startTime;
                    startTime = endTime;
                    endTime = temp;
                }

                result.put("startTime", startTime.format(DATE_TIME_FORMATTER));
                result.put("endTime", endTime.format(DATE_TIME_FORMATTER));

                return result;
            } catch (DateTimeParseException e) {
                // 日期解析错误，使用timeRangeType
                return getTimeRange(timeRangeType);
            }
        }

        // 否则使用时间范围类型计算
        return getTimeRange(timeRangeType);
    }
} 