package org.jeecg.modules.data.spatiotemporal.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 热力图响应VO
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
public class HeatmapResponseVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 记录列表
     */
    private List<HeatPointVO> records;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 总页数
     */
    private Integer pages;

    /**
     * 当前页
     */
    private Integer current;

    /**
     * 页大小
     */
    private Integer size;

    /**
     * 聚合数据
     */
    private List<HeatMapAggregationVO> aggregations;

    /**
     * 最大数量
     */
    private Integer maxCount;
} 