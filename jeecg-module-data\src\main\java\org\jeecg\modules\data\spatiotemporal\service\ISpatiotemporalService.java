package org.jeecg.modules.data.spatiotemporal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.data.spatiotemporal.dto.SpatiotemporalQueryDTO;
import org.jeecg.modules.data.spatiotemporal.entity.FlightInspectionImage;
import org.jeecg.modules.data.spatiotemporal.vo.HeatmapResponseVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

/**
 * 时空搜图服务接口
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface ISpatiotemporalService {

    /**
     * 获取热力图数据
     *
     * @param queryDTO 查询参数
     * @return 热力图数据
     */
    HeatmapResponseVO getHeatmapData(SpatiotemporalQueryDTO queryDTO);

    /**
     * 获取热力图颜色配置
     *
     * @return 颜色配置
     */
    Map<String, String> getHeatmapColorConfig();

    /**
     * 获取初始中心点
     *
     * @return 中心点坐标
     */
    Map<String, Object> getInitialCenter();

    /**
     * 图片查询方法
     *
     * @param queryDTO 查询参数
     * @param needPaging 是否需要分页
     * @param sortByCaptureTimeAsc 是否按拍摄时间升序排列
     * @return 查询结果
     */
    Page<FlightInspectionImage> queryImages(
            SpatiotemporalQueryDTO queryDTO,
            Boolean needPaging,
            Boolean sortByCaptureTimeAsc);



    /**
     * 批量删除图片
     *
     * @param ids 图片ID集合，以逗号分隔
     * @return 删除结果
     */
    boolean batchDeleteImages(String ids);



    /**
     * 根据ID获取图片
     *
     * @param imageId 图片ID
     * @return 图片信息
     */
    FlightInspectionImage getImageById(Long imageId);

    /**
     * 获取数据库中图片总数
     *
     * @return 图片总数
     */
    int getTotalImagesCount();
}