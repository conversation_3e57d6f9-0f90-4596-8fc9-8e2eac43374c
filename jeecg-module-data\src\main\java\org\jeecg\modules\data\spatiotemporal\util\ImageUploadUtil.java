package org.jeecg.modules.data.spatiotemporal.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * 图片上传工具类
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Slf4j
@Component
public class ImageUploadUtil {

    // 上传目录的基础路径
    private static String uploadBasePath;

    // 允许的图片扩展名
    private static final String[] ALLOWED_EXTENSIONS = {"jpg", "jpeg", "png", "gif", "bmp"};

    // 生成缩略图的默认尺寸
    private static final int THUMBNAIL_WIDTH = 200;
    private static final int THUMBNAIL_HEIGHT = 200;

    @Value("${upload.path:/opt/upFiles/images}")
    public void setUploadBasePath(String path) {
        uploadBasePath = path;
        // 确保上传目录存在
        createDirectory(uploadBasePath);

        // 创建缩略图目录
        createDirectory(uploadBasePath + "/thumbnail");

        log.info("设置上传基础路径: {}", uploadBasePath);
    }

    /**
     * 上传图片
     *
     * @param file     图片文件
     * @param subDir   子目录名，如 "upload/images"
     * @return 存储路径
     * @throws IOException IO异常
     */
    public static String uploadImage(MultipartFile file, String subDir) throws IOException {
        // 检查文件是否有效
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件为空或无效");
        }

        // 获取原始文件名和扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new IllegalArgumentException("无法获取文件名");
        }

        String extension = FilenameUtils.getExtension(originalFilename).toLowerCase();
        if (!isAllowedExtension(extension)) {
            throw new IllegalArgumentException("不允许的文件类型: " + extension);
        }

        // 创建子目录
        String fullPath = uploadBasePath;
        if (StringUtils.isNotBlank(subDir)) {
            fullPath = uploadBasePath + "/" + subDir;
            createDirectory(fullPath);
        }

        // 生成唯一文件名
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        String dateDir = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String saveDir = fullPath + "/" + dateDir;

        // 确保日期目录存在
        createDirectory(saveDir);

        // 保存文件
        String fileName = uuid + "." + extension;
        String filePath = saveDir + "/" + fileName;
        File dest = new File(filePath);
        file.transferTo(dest);

        // 相对路径，用于数据库存储
        String relativePath = "";
        if (StringUtils.isNotBlank(subDir)) {
            relativePath = subDir + "/";
        }
        relativePath += dateDir + "/" + fileName;

        log.info("文件上传成功: {}", relativePath);

        // 创建缩略图
        try {
            createThumbnail(dest, extension);
        } catch (Exception e) {
            log.error("创建缩略图失败", e);
            // 缩略图创建失败不影响主流程
        }

        return relativePath;
    }

    /**
     * 删除图片
     *
     * @param relativePath 相对路径
     * @throws IOException IO异常
     */
    public static void deleteImage(String relativePath) throws IOException {
        if (StringUtils.isBlank(relativePath)) {
            throw new IllegalArgumentException("相对路径为空");
        }

        // 删除原图
        String fullPath = uploadBasePath + "/" + relativePath;
        Path path = Paths.get(fullPath);
        if (Files.exists(path)) {
            Files.delete(path);
            log.info("删除文件: {}", fullPath);
        }

        // 尝试删除缩略图
        try {
            String thumbnailPath = getThumbnailPath(relativePath);
            Path thumbPath = Paths.get(uploadBasePath + "/" + thumbnailPath);
            if (Files.exists(thumbPath)) {
                Files.delete(thumbPath);
                log.info("删除缩略图: {}", thumbnailPath);
            }
        } catch (Exception e) {
            log.error("删除缩略图失败", e);
        }
    }

    /**
     * 生成缩略图
     *
     * @param imageFile 原图文件
     * @param extension 文件扩展名
     * @throws IOException IO异常
     */
    private static void createThumbnail(File imageFile, String extension) throws IOException {
        if (!imageFile.exists()) {
            throw new IllegalArgumentException("原图文件不存在");
        }

        // 读取原图
        BufferedImage originalImage = ImageIO.read(imageFile);
        if (originalImage == null) {
            throw new IOException("无法读取图片文件: " + imageFile.getAbsolutePath());
        }

        // 计算缩略图尺寸，保持宽高比
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();

        int targetWidth = THUMBNAIL_WIDTH;
        int targetHeight = (int) (((double) originalHeight / originalWidth) * targetWidth);

        // 如果宽度仍然大于高度，调整为使用高度来计算
        if (targetHeight > THUMBNAIL_HEIGHT) {
            targetHeight = THUMBNAIL_HEIGHT;
            targetWidth = (int) (((double) originalWidth / originalHeight) * targetHeight);
        }

        // 创建缩略图
        BufferedImage thumbnail = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = thumbnail.createGraphics();

        // 使用高质量缩放
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        g.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
        g.dispose();

        // 保存缩略图
        String thumbnailPath = createThumbnailPath(imageFile.getAbsolutePath());
        File thumbnailFile = new File(thumbnailPath);

        // 确保缩略图目录存在
        File parentDir = thumbnailFile.getParentFile();
        if (!parentDir.exists() && !parentDir.mkdirs()) {
            throw new IOException("无法创建缩略图目录: " + parentDir.getAbsolutePath());
        }

        // 将缩略图写入文件
        ImageIO.write(thumbnail, extension, thumbnailFile);
        log.info("创建缩略图成功: {}", thumbnailFile.getAbsolutePath());
    }




    /**
     * 生成缩略图路径
     *
     * @param originalPath 原图路径
     * @return 缩略图路径
     */
    private static String createThumbnailPath(String originalPath) {
        if (StringUtils.isBlank(originalPath)) {
            return null;
        }

        // 替换原始路径中的基础路径为包含thumbnail的路径
        String baseDirName = new File(uploadBasePath).getName();

        if (originalPath.contains(baseDirName)) {
            int index = originalPath.indexOf(baseDirName);
            String prefix = originalPath.substring(0, index + baseDirName.length());
            String suffix = originalPath.substring(index + baseDirName.length());

            // 查找下一个路径分隔符
            int slashIndex = suffix.indexOf(File.separator);
            if (slashIndex >= 0) {
                return prefix + suffix.substring(0, slashIndex) + File.separator + "thumbnail" + suffix.substring(slashIndex);
            } else {
                return prefix + File.separator + "thumbnail" + suffix;
            }
        }

        // 简单返回原路径添加thumbnail前缀
        return FilenameUtils.getFullPath(originalPath) + "thumbnail/" + FilenameUtils.getName(originalPath);
    }

    /**
     * 获取缩略图相对路径
     *
     * @param relativePath 原图相对路径
     * @return 缩略图相对路径
     */
    private static String getThumbnailPath(String relativePath) {
        if (StringUtils.isBlank(relativePath)) {
            return null;
        }

        int lastSlashIndex = relativePath.lastIndexOf("/");
        if (lastSlashIndex >= 0) {
            return relativePath.substring(0, lastSlashIndex) + "/thumbnail" +
                    relativePath.substring(lastSlashIndex);
        } else {
            return "thumbnail/" + relativePath;
        }
    }

    /**
     * 检查扩展名是否在允许列表中
     *
     * @param extension 扩展名
     * @return 是否允许
     */
    private static boolean isAllowedExtension(String extension) {
        if (StringUtils.isBlank(extension)) {
            return false;
        }

        for (String allowed : ALLOWED_EXTENSIONS) {
            if (allowed.equalsIgnoreCase(extension)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 创建目录
     *
     * @param dirPath 目录路径
     * @throws IOException IO异常
     */
    private static void createDirectory(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists() && !dir.mkdirs()) {
            log.error("无法创建目录: {}", dirPath);
        }
    }
} 