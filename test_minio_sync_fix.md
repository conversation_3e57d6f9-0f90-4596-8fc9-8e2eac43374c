# MinIO同步问题修复报告

## 问题描述
- **任务ID**: `1942849415252844546`
- **实际文件路径**: `djicloudapi/wayline/14d45692-a92b-4abe-b9d9-f23783d5aa93/DJI_202507091534_106_1942849415252844546`
- **错误信息**: "任务ID 1942849415252844546 对应的文件夹在MinIO中不存在"
- **错误位置**: `MinioSyncServiceImpl.java:529`

## 问题根因分析
在`findWaylinePathsForTaskId`方法中，调用`extractTaskId(objectName)`时传入的是**原始路径**（包含`djicloudapi`前缀），但`extractTaskId`方法期望的是**标准化后的路径**（去掉`djicloudapi`前缀）。

### 代码问题位置
```java
// 问题代码（第827行）
String extractedTaskId = extractTaskId(objectName);  // objectName包含djicloudapi前缀

// extractTaskId方法期望的输入格式
// 标准化格式: wayline/uuid/DJI_xxx_taskId/image.jpg
// 实际输入格式: djicloudapi/wayline/uuid/DJI_xxx_taskId/image.jpg
```

## 修复内容

### 1. 修复路径标准化问题
**文件**: `MinioSyncServiceImpl.java`
**位置**: `findWaylinePathsForTaskId`方法

**修复前**:
```java
String extractedTaskId = extractTaskId(objectName);
```

**修复后**:
```java
// 先标准化路径，然后提取任务ID进行验证
String standardizedPath = standardizeStoragePath(objectName);
String extractedTaskId = extractTaskId(standardizedPath);
```

### 2. 优化wayline路径解析逻辑
**文件**: `MinioSyncServiceImpl.java`
**位置**: `extractTaskId`方法

增加了对3部分文件夹名称格式的支持：
- 4部分格式: `DJI_时间戳_数字_任务ID`
- 3部分格式: `DJI_时间戳_任务ID`

## 修复后的路径解析流程

### 步骤详解
1. **原始路径**: `djicloudapi/wayline/14d45692-a92b-4abe-b9d9-f23783d5aa93/DJI_202507091534_106_1942849415252844546`

2. **标准化处理**:
   - 调用`standardizeStoragePath()`
   - 去掉`djicloudapi/`前缀
   - 结果: `wayline/14d45692-a92b-4abe-b9d9-f23783d5aa93/DJI_202507091534_106_1942849415252844546`

3. **路径分割**:
   - 按`/`分割: `["wayline", "14d45692-a92b-4abe-b9d9-f23783d5aa93", "DJI_202507091534_106_1942849415252844546"]`
   - 提取文件夹名称: `DJI_202507091534_106_1942849415252844546`

4. **任务ID提取**:
   - 按`_`分割: `["DJI", "202507091534", "106", "1942849415252844546"]`
   - 取最后一部分: `1942849415252844546`
   - 验证是否为纯数字: ✅

5. **路径匹配**:
   - 提取的任务ID与目标任务ID匹配: ✅
   - 生成wayline路径: `djicloudapi/wayline/14d45692-a92b-4abe-b9d9-f23783d5aa93/DJI_202507091534_106_1942849415252844546/`

## 测试步骤

### 后端测试
1. **重新编译后端代码**
   ```bash
   mvn clean compile
   ```

2. **重启后端服务**
   - 停止当前运行的服务
   - 重新启动Spring Boot应用

### 前端测试
3. **使用前端界面测试**
   - 打开MinIO同步管理页面
   - 输入任务ID: `1942849415252844546`
   - 点击"指定任务同步"按钮
   - 观察同步结果

4. **使用API直接测试**
   ```bash
   curl -X POST "http://localhost:8080/data/inspection/syncMinioImages" \
        -d "taskId=1942849415252844546" \
        -d "bucketName=djicloudapi"
   ```

## 预期结果

### 成功指标
- ✅ 系统能够找到wayline路径下的文件夹
- ✅ 不再出现"文件夹在MinIO中不存在"的错误
- ✅ 同步操作成功执行
- ✅ 日志显示找到新格式路径: `[djicloudapi/wayline/14d45692-a92b-4abe-b9d9-f23783d5aa93/DJI_202507091534_106_1942849415252844546/]`

### 日志输出示例
```
任务ID 1942849415252844546 路径检查结果: 找到新格式路径: [djicloudapi/wayline/14d45692-a92b-4abe-b9d9-f23783d5aa93/DJI_202507091534_106_1942849415252844546/]
```

## 影响范围
- **修复范围**: 仅影响wayline格式路径的任务ID识别
- **兼容性**: 保持对传统路径格式的完全兼容
- **性能影响**: 无显著性能影响，仅增加一次路径标准化调用

## 新发现的问题：数据库字段长度不够

### 问题描述
修复路径识别问题后，出现了新的错误：
```
Data truncation: Data too long for column 'storage_path' at row 1
```

### 问题原因
wayline格式的路径比传统格式更长：
- **传统格式**: `1942849415252844546/image.jpg` (约30字符)
- **wayline格式**: `wayline/14d45692-a92b-4abe-b9d9-f23783d5aa93/DJI_202507091534_106_1942849415252844546/DJI_20250709153601_0001_T.jpeg` (约118字符)

### 解决方案
需要修改数据库表结构，增加`storage_path`字段的长度：

```sql
ALTER TABLE flight_inspection_images
MODIFY COLUMN storage_path VARCHAR(500) NOT NULL COMMENT '存储路径';
```

## 完整解决步骤

### 1. 后端代码修复 ✅
- 修复了`findWaylinePathsForTaskId`方法中的路径标准化问题
- 优化了`extractTaskId`方法的wayline路径解析逻辑
- 更新了`extractWaylineFolderPath`方法支持两种路径格式

### 2. 数据库结构修复 ⚠️
执行SQL语句修改字段长度：
```sql
ALTER TABLE flight_inspection_images
MODIFY COLUMN storage_path VARCHAR(500) NOT NULL COMMENT '存储路径';
```

### 3. 测试验证
- ✅ 路径识别：成功找到wayline路径
- ✅ 任务ID提取：正确提取任务ID `1942849415252844546`
- ⚠️ 数据库插入：需要修改字段长度后重新测试

## 测试结果

### 成功的部分
```
任务ID 1942849415252844546 路径检查结果: 找到新格式路径: [wayline/14d45692-a92b-4abe-b9d9-f23783d5aa93/DJI_202507091534_106_1942849415252844546/]
waylinePrefixes数量: 1, 内容: [wayline/14d45692-a92b-4abe-b9d9-f23783d5aa93/DJI_202507091534_106_1942849415252844546/]
```

### 需要解决的问题
```
Data truncation: Data too long for column 'storage_path' at row 1
```

## 相关文件
- `jeecg-module-data/src/main/java/org/jeecg/modules/data/spatiotemporal/service/impl/MinioSyncServiceImpl.java`
  - `findWaylinePathsForTaskId()` 方法
  - `extractTaskId()` 方法
  - `extractWaylineFolderPath()` 方法
- `fix_storage_path_length.sql` - 数据库字段长度修复脚本
